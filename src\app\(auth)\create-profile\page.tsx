
'use client';

import React, { useEffect, useState } from 'react';
import { useRouter } from 'next/navigation';
import { useForm, type SubmitHandler } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import { useAuth } from '@/contexts/auth-context';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip";
import { <PERSON>, UserPlus, Info, User as UserIcon } from 'lucide-react';
import { useToast } from '@/hooks/use-toast';
import type { CreateProfileInput, CreateProfileOutput } from '@/ai/flows/profile-creation';
import { createUserProfile as createUserProfileFlow } from '@/ai/flows/profile-creation';
import type { UserProfile } from '@/types';

const profileSchema = z.object({
  displayName: z.string().min(2, "El nombre es requerido y debe tener al menos 2 caracteres."),
  age: z.coerce.number().min(1, "La edad es requerida").max(120, "La edad debe ser válida"),
  gender: z.string().min(1, "El género es requerido"),
  therapistGender: z.enum(['male', 'female'], { required_error: "Por favor, selecciona una preferencia." }),
  mentalHealthHistory: z.string().min(1, "Por favor, proporciona algún contexto sobre tu bienestar emocional o indica si no tienes."),
  preferences: z.string().min(1, "Por favor, describe tus preferencias de interacción."),
});

type ProfileFormData = z.infer<typeof profileSchema>;

export default function CreateProfilePage() {
  const { user, userProfile, saveUserProfile, initialLoading, updateUserAuthProfile } = useAuth();
  const router = useRouter();
  const { toast } = useToast();
  const [isSubmitting, setIsSubmitting] = useState(false);

  const form = useForm<ProfileFormData>({
    resolver: zodResolver(profileSchema),
    defaultValues: {
      displayName: user?.displayName || '',
      age: undefined,
      gender: '',
      therapistGender: undefined,
      mentalHealthHistory: '',
      preferences: '',
    },
  });

  useEffect(() => {
    if (!initialLoading && !user) {
      router.replace('/login');
    } else if (!initialLoading && user && userProfile) {
      router.replace('/chat');
    }
    if (user?.displayName && !form.getValues('displayName')) {
      form.setValue('displayName', user.displayName);
    }
  }, [user, userProfile, initialLoading, router, form]);

  const onSubmit: SubmitHandler<ProfileFormData> = async (data) => {
    if (!user) return;
    setIsSubmitting(true);
    try {
      await updateUserAuthProfile({ displayName: data.displayName });

      const aiInput: CreateProfileInput = {
        age: data.age,
        gender: data.gender,
        email: user.email || undefined,
        mentalHealthHistory: data.mentalHealthHistory,
        preferences: data.preferences,
      };
      const aiResponse: CreateProfileOutput = await createUserProfileFlow(aiInput);

      const newUserProfile: UserProfile = {
        uid: user.uid,
        email: user.email,
        displayName: data.displayName,
        age: data.age,
        gender: data.gender,
        therapistGender: data.therapistGender,
        mentalHealthHistory: data.mentalHealthHistory,
        preferences: data.preferences,
        personalizedGreeting: aiResponse.personalizedGreeting,
        initialRecommendations: aiResponse.initialRecommendations,
        createdAt: new Date(),
      };

      await saveUserProfile(user.uid, newUserProfile);

      toast({ title: "¡Perfil Creado!", description: aiResponse.personalizedGreeting });
      router.replace('/chat');
    } catch (error) {
      toast({ title: "Falló la Creación de Perfil", variant: "destructive" });
    } finally {
      setIsSubmitting(false);
    }
  };

  if (initialLoading) {
    return (
      <div className="flex h-screen w-screen items-center justify-center bg-background">
        <Brain className="h-16 w-16 animate-pulse text-primary" />
        <p className="ml-4 text-lg">Verificando estado...</p>
      </div>
    );
  }
  
  return (
    <TooltipProvider>
      <div className="flex min-h-screen flex-col items-center justify-center bg-gradient-to-br from-background to-secondary p-4 py-12">
        <Card className="w-full max-w-lg shadow-2xl">
          <CardHeader>
             {/* ... */}
          </CardHeader>
          <CardContent>
            <Form {...form}>
              <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
                <FormField control={form.control} name="displayName" render={({ field }) => ( <FormItem> {/* ... */} </FormItem> )} />
                <FormField control={form.control} name="age" render={({ field }) => ( <FormItem> {/* ... */} </FormItem> )} />
                <FormField control={form.control} name="gender" render={({ field }) => ( <FormItem> {/* ... */} </FormItem> )} />
                
                <FormField
                  control={form.control}
                  name="therapistGender"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Preferencia de Terapeuta de IA</FormLabel>
                      <Select onValueChange={field.onChange} defaultValue={field.value}>
                        <FormControl>
                          <SelectTrigger><SelectValue placeholder="Selecciona el género para tu IA" /></SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          <SelectItem value="female">Femenino</SelectItem>
                          <SelectItem value="male">Masculino</SelectItem>
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                
                <FormField control={form.control} name="mentalHealthHistory" render={({ field }) => ( <FormItem> {/* ... */} </FormItem> )} />
                <FormField control={form.control} name="preferences" render={({ field }) => ( <FormItem> {/* ... */} </FormItem> )} />

                <CardFooter className="px-0 pt-6">
                  <Button type="submit" disabled={isSubmitting} className="w-full text-lg py-6">{isSubmitting ? 'Creando Perfil...' : 'Completar Perfil'}</Button>
                </CardFooter>
              </form>
            </Form>
          </CardContent>
        </Card>
      </div>
    </TooltipProvider>
  );
}
