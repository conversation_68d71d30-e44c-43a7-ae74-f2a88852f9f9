
'use server';
import { ai } from '@/ai/genkit';
import { z } from 'genkit';
import { generateImageFromTopic } from './generate-image-from-topic';

// --- ZOD SCHEMAS ---
const GenerateArticleInputSchema = z.object({
  uid: z.string().describe('El UID del usuario autenticado.'),
  topic: z.string().describe('El tema del artículo.'),
});
export type GenerateArticleInput = z.infer<typeof GenerateArticleInputSchema>;

const GenerateArticleOutputSchema = z.object({
  title: z.string().describe('El título del artículo.'),
  content: z.string().describe('El contenido del artículo.'),
  imageUrl: z.string().optional().describe('La URL de una imagen relacionada.'),
});
export type GenerateArticleOutput = z.infer<typeof GenerateArticleOutputSchema>;


// --- FUNCIÓN PRINCIPAL (LÓGICA DE PAGO DESACTIVADA) ---
export async function generateArticle(input: GenerateArticleInput): Promise<GenerateArticleOutput> {
  // Lógica de pago y límites deshabilitada temporalmente.
  // Se asume que todos los usuarios son premium para que la app funcione.

  const articlePrompt = ai.definePrompt({
      name: 'generateArticlePrompt',
      input: { schema: z.object({ topic: z.string() }) },
      output: { schema: z.object({ title: z.string(), content: z.string() }) },
      prompt: `Eres un asistente de IA especializado en generar artículos breves y prácticos sobre salud mental. Basado en el tema proporcionado, crea un artículo de 150-200 palabras con consejos accionables. La respuesta DEBE ESTAR EN ESPAÑOL.

Tema: {{{topic}}}`,
  });

  const { output: articleOutput } = await articlePrompt({ topic: input.topic });

  if (!articleOutput) {
    throw new Error('No se pudo generar el contenido del artículo.');
  }

  let imageUrl = '';
  try {
    const imageResult = await generateImageFromTopic({ topic: input.topic });
    if (imageResult?.imageUrl) {
      imageUrl = imageResult.imageUrl;
    }
  } catch (imageError) {
    console.warn(`Error generando imagen para el tema "${input.topic}":`, imageError);
  }

  return {
    title: articleOutput.title,
    content: articleOutput.content,
    imageUrl: imageUrl || undefined,
  };
}
