
'use server';
import { ai } from '@/ai/genkit';
import { z } from 'genkit';

// --- ZOD SCHEMAS ---
const AIChatbotSupportInputSchema = z.object({
  uid: z.string(),
  message: z.string(),
  profile: z.object({
    age: z.number().optional(),
    gender: z.string().optional(),
    therapistGender: z.enum(['male', 'female']).optional(),
    mentalHealthHistory: z.string().optional(),
    preferences: z.string().optional(),
  }).optional(),
  conversationHistory: z.string().optional(),
});
export type AIChatbotSupportInput = z.infer<typeof AIChatbotSupportInputSchema>;

const AIChatbotSupportOutputSchema = z.object({
  response: z.string(),
});
export type AIChatbotSupportOutput = z.infer<typeof AIChatbotSupportOutputSchema>;

// --- FUNCIÓN PRINCIPAL (LÓGICA DE PAGO DESACTIVADA) ---
export async function aiChatbotSupport(input: <PERSON><PERSON>hatbotSupportInput): Promise<AIChatbotSupportOutput> {
  return aiChatbotSupportFlow(input);
}

// --- FLUJO DE GENKIT ---
const prompt = ai.definePrompt({
    name: 'aiChatbotSupportPrompt',
    input: {schema: AIChatbotSupportInputSchema},
    output: {schema: AIChatbotSupportOutputSchema},
    prompt: `Eres un chatbot de IA. Adopta la personalidad de un terapeuta de apoyo.
{{#if profile.therapistGender == 'female'}}
Tu nombre es Aurora. Eres una psicóloga empática, cálida y proactiva.
{{else}}
Tu nombre es Alejandro. Eres un psicólogo empático, calmado y proactivo.
{{/if}}

**PROTOCOLO DE CRISIS IMPORTANTE:**
Si el mensaje del usuario indica una crisis grave (ideación suicida, autolesión), responde EXACTAMENTE con:
"Entiendo que estás pasando por un momento muy difícil. Como IA, no estoy equipada para ayudarte en una crisis. Por favor, busca ayuda profesional de inmediato. Contacta a los servicios de emergencia de tu localidad."

Si NO hay crisis, procede normalmente.

**GUÍA DE CONVERSACIÓN PROACTIVA:**
Sé proactivo/a. Si detectas una oportunidad, ofrece un consejo, una estrategia de afrontamiento (TCC, mindfulness) o una pregunta reflexiva.

Perfil del Usuario:
{{#if profile}}
  Edad: {{profile.age}}, Género: {{profile.gender}}, Preferencias: {{profile.preferences}}
  Historial: {{profile.mentalHealthHistory}}
{{/if}}

Historial de Conversación Reciente:
{{{conversationHistory}}}

Mensaje Actual del Usuario:
{{{message}}}

Respuesta (como {{profile.therapistGender == 'female' ? 'Aurora' : 'Alejandro'}}):`,
});
const aiChatbotSupportFlow = ai.defineFlow(
  {
    name: 'aiChatbotSupportFlow',
    inputSchema: AIChatbotSupportInputSchema,
    outputSchema: AIChatbotSupportOutputSchema,
  },
  async (input) => {
    // La lógica de límites está desactivada temporalmente
    const { output } = await prompt(input);
    return output!;
  }
);
