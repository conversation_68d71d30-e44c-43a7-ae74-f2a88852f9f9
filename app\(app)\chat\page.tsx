
'use client';

import React, { useState, useRef, useEffect } from 'react';
import { useAuth } from '@/contexts/auth-context';
import type { ChatMessage } from '@/types';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Send, AlertTriangle, Sparkles } from 'lucide-react'; 
import { Card, CardContent, CardHeader, CardTitle, CardFooter } from '@/components/ui/card'; 
import { aiChatbotSupport, type AIChatbotSupportInput } from '@/ai/flows/ai-chatbot-support';
import { Skeleton } from '@/components/ui/skeleton';
import { useRouter } from 'next/navigation';

export default function ChatPage() {
  const { user, userProfile } = useAuth();
  const router = useRouter();
  const [messages, setMessages] = useState<ChatMessage[]>([]);
  const [input, setInput] = useState('');
  const [isLoadingAiResponse, setIsLoadingAiResponse] = useState(false);
  const [isLimitReached, setIsLimitReached] = useState(false);
  const scrollAreaRef = useRef<HTMLDivElement>(null);

  const therapistGender = userProfile?.therapistGender || 'female';
  const therapistAvatar = therapistGender === 'male' 
    ? '/images/avatar-terapeuta-hombre.png' 
    : '/images/avatar-terapeuta-mujer.png';
  const therapistName = therapistGender === 'male' ? 'Psicólogo Alejandro' : 'Psicóloga Aurora';

   const getInitials = (name?: string) => {
    if (!name) return '?';
    const names = name.split(' ');
    if (names.length > 1) {
      return `${names[0][0]}${names[names.length - 1][0]}`.toUpperCase();
    }
    return name.substring(0, 2).toUpperCase();
  };

  useEffect(() => {
    if (userProfile?.personalizedGreeting && messages.length === 0) {
      setMessages([{ id: 'initial-greeting', text: userProfile.personalizedGreeting, sender: 'ai', timestamp: new Date() }]);
    }
  }, [userProfile, messages.length]);

  useEffect(() => {
    if (scrollAreaRef.current) {
      scrollAreaRef.current.scrollTo({ top: scrollAreaRef.current.scrollHeight, behavior: 'smooth'});
    }
  }, [messages]);

  const handleSendMessage = async () => {
    if (input.trim() === '' || !user || !userProfile) return;

    const userMessage: ChatMessage = { id: Date.now().toString(), text: input, sender: 'user', timestamp: new Date() };
    setMessages((prevMessages) => [...prevMessages, userMessage]);
    setInput('');
    setIsLoadingAiResponse(true);

    const conversationHistory = ""; // Desactivado temporalmente

    try {
      const chatbotInput: AIChatbotSupportInput = {
        uid: user.uid,
        message: userMessage.text,
        profile: {
          ...userProfile,
          therapistGender: therapistGender,
        },
        conversationHistory: conversationHistory || undefined,
      };
      
      const aiResponse = await aiChatbotSupport(chatbotInput);
      
      const aiMessage: ChatMessage = { id: (Date.now() + 1).toString(), text: aiResponse.response, sender: 'ai', timestamp: new Date() };
      setMessages((prevMessages) => [...prevMessages, aiMessage]);

    } catch (error: any) {
      if (error.message.includes('USAGE_LIMIT_REACHED')) {
        setIsLimitReached(true);
      } else {
        const errorMessage: ChatMessage = { id: 'generic-error', text: "Lo siento, encontré un error.", sender: 'ai', timestamp: new Date() };
        setMessages((prevMessages) => [...prevMessages, errorMessage]);
      }
    } finally {
      setIsLoadingAiResponse(false);
    }
  };
  
  const Paywall = () => (
    <div className="p-4 border-t text-center">
        <h3 className="font-bold">Límite alcanzado</h3>
        <p className="text-sm text-muted-foreground mb-2">Actualiza a Premium para continuar.</p>
        <Button onClick={() => router.push('/subscribe')}>Ver Planes</Button>
    </div>
  );

  return (
    <Card className="h-[calc(100vh-120px)] flex flex-col shadow-lg">
      <CardHeader>
        <CardTitle className="text-2xl font-headline flex items-center">
          <Avatar className="mr-2 h-10 w-10"><AvatarImage src={therapistAvatar} /><AvatarFallback>IA</AvatarFallback></Avatar>
          {therapistName}
        </CardTitle>
      </CardHeader>
      <CardContent className="flex-grow flex flex-col overflow-hidden p-0">
        <ScrollArea className="flex-grow p-4" ref={scrollAreaRef}>
          {messages.map((msg) => (
            <div key={msg.id} className={`mb-4 flex items-end gap-2 ${msg.sender === 'user' ? 'justify-end' : 'justify-start'}`}>
              {msg.sender === 'ai' && (<Avatar className="h-10 w-10"><AvatarImage src={therapistAvatar} /><AvatarFallback>IA</AvatarFallback></Avatar>)}
              <div className={`max-w-[70%] rounded-xl px-4 py-3 shadow ${msg.sender === 'user' ? 'bg-primary text-primary-foreground' : 'bg-muted'}`}>
                <p className="text-sm whitespace-pre-wrap">{msg.text}</p>
                <p className={`mt-1 text-xs ${msg.sender === 'user' ? 'text-primary-foreground/70' : 'text-muted-foreground/70'} text-right`}>
                  {new Date(msg.timestamp).toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}
                </p>
              </div>
              {msg.sender === 'user' && user && (<Avatar className="h-10 w-10"><AvatarImage src={user.photoURL || undefined} /><AvatarFallback>{getInitials(userProfile?.displayName)}</AvatarFallback></Avatar>)}
            </div>
          ))}
          {isLoadingAiResponse && ( <Skeleton className="h-12 w-2/3" /> )}
        </ScrollArea>
        {isLimitReached ? <Paywall /> : (
          <div className="border-t p-4"><form onSubmit={(e) => { e.preventDefault(); handleSendMessage(); }} className="flex items-center gap-2">
              <Input value={input} onChange={(e) => setInput(e.target.value)} placeholder="Escribe tu mensaje..." disabled={isLoadingAiResponse} />
              <Button type="submit" size="icon" disabled={isLoadingAiResponse || input.trim() === ''}><Send className="h-5 w-5" /></Button>
          </form></div>
        )}
      </CardContent>
      <CardFooter className="p-3 border-t bg-muted/50">
          <div className="flex items-center text-xs text-muted-foreground"><AlertTriangle className="h-4 w-4 mr-2" /><span>Esta IA es un apoyo, no un reemplazo para ayuda profesional.</span></div>
      </CardFooter>
    </Card>
  );
}
