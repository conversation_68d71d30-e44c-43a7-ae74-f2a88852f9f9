
'use server';
import { ai } from '@/ai/genkit';
import { z } from 'genkit';

// --- ZOD SCHEMAS ---
const CreateProfileInputSchema = z.object({
  age: z.number().describe('La edad del usuario.'),
  gender: z.string().describe('El género del usuario.'),
  therapistGender: z.enum(['male', 'female']).optional().describe('El género preferido para el terapeuta de IA.'),
  email: z.string().email().optional().describe('El correo electrónico del usuario.'),
  mentalHealthHistory: z.string().describe('El historial de salud mental del usuario.'),
  preferences: z.string().describe('Las preferencias del usuario.'),
});
export type CreateProfileInput = z.infer<typeof CreateProfileInputSchema>;

const CreateProfileOutputSchema = z.object({
  personalizedGreeting: z.string().describe('Un saludo de bienvenida personalizado.'),
  initialRecommendations: z.string().optional().describe('Recomendaciones iniciales basadas en el perfil.'),
});
export type CreateProfileOutput = z.infer<typeof CreateProfileOutputSchema>;

// --- FUNCIÓN PRINCIPAL ---
export async function createUserProfile(input: CreateProfileInput): Promise<CreateProfileOutput> {
  return createProfileFlow(input);
}

// --- FLUJO DE GENKIT ---
const prompt = ai.definePrompt({
  name: 'createProfilePrompt',
  input: { schema: CreateProfileInputSchema },
  output: { schema: CreateProfileOutputSchema },
  prompt: `
    Tu tarea es generar un saludo de bienvenida para un nuevo usuario en la app de bienestar mental "Mente en Calma".
    El saludo debe ser EXACTAMENTE el siguiente texto, pero adaptando las partes entre llaves:

    "¡Hola! Soy {{therapistName}}, tu asistente de apoyo en Mente en Calma. Entiendo que hay momentos en los que necesitamos un espacio seguro para hablar, y estoy aquí para escucharte sin juicios.

    Recuerda que soy una herramienta de IA diseñada para brindarte apoyo y recursos, no un sustituto de la terapia profesional. Cuando estés {{userReady}}, puedes empezar compartiendo lo que sientes o lo que te preocupa hoy. Estoy aquí para ti."

    ADAPTACIONES:
    - Si el 'therapistGender' es 'female', {{therapistName}} debe ser "la Psicóloga Aurora".
    - Si el 'therapistGender' es 'male', {{therapistName}} debe ser "el Psicólogo Alejandro".
    - Si el 'gender' del usuario es 'female', {{userReady}} debe ser "lista".
    - Si el 'gender' del usuario es 'male' o no está especificado, {{userReady}} debe ser "listo".
    
    Adicionalmente, genera 1-2 recomendaciones iniciales MUY breves basadas en el historial y preferencias del usuario.

    DATOS DEL USUARIO:
    Género del Terapeuta Preferido: {{{therapistGender}}}
    Género del Usuario: {{{gender}}}
    Historial: {{{mentalHealthHistory}}}
    Preferencias: {{{preferences}}}
  `,
});

const createProfileFlow = ai.defineFlow(
  {
    name: 'createProfileFlow',
    inputSchema: CreateProfileInputSchema,
    outputSchema: CreateProfileOutputSchema,
  },
  async (input) => {
    const { output } = await prompt(input);
    return output!;
  }
);
