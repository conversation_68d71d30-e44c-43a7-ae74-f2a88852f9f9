
'use server';
/**
 * @fileOverview A flow to generate an image based on a topic.
 *
 * - generateImageFromTopic - A function that handles image generation.
 * - GenerateImageInput - The input type for the generateImageFromTopic function.
 * - GenerateImageOutput - The return type for the generateImageFromTopic function.
 */

import {ai} from '@/ai/genkit';
import {z} from 'genkit';

const GenerateImageInputSchema = z.object({
  topic: z.string().describe('The topic to generate an image for. This will be used as the image prompt.'),
});
export type GenerateImageInput = z.infer<typeof GenerateImageInputSchema>;

const GenerateImageOutputSchema = z.object({
  imageUrl: z.string().url().describe('The data URI of the generated image.'),
});
export type GenerateImageOutput = z.infer<typeof GenerateImageOutputSchema>;

export async function generateImageFromTopic(input: GenerateImageInput): Promise<GenerateImageOutput> {
  return generateImageFromTopicFlow(input);
}

const generateImageFromTopicFlow = ai.defineFlow(
  {
    name: 'generateImageFromTopicFlow',
    inputSchema: GenerateImageInputSchema,
    outputSchema: GenerateImageOutputSchema,
  },
  async (input) => {
    try {
      const imagePrompt = `A visually appealing and symbolic image representing the concept of: "${input.topic}". The style should be modern, clean, and suitable for a mental wellness app.`;
      const {media} = await ai.generate({
        model: 'googleai/gemini-2.0-flash-exp', // IMPORTANT: Use this specific model for image generation
        prompt: imagePrompt,
        config: {
          responseModalities: ['TEXT', 'IMAGE'], // MUST provide both
        },
      });

      if (media?.url) {
        return { imageUrl: media.url };
      } else {
        console.warn('Image generation did not return a URL for topic:', input.topic);
        // Fallback or specific error image could be returned here if desired
        return { imageUrl: '' }; // Return empty string or a placeholder data URI
      }
    } catch (error) {
      console.error('Error generating image for topic:', input.topic, error);
      return { imageUrl: '' }; // Return empty string or a placeholder data URI on error
    }
  }
);
