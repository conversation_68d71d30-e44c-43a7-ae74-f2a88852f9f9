
'use client';

import React, { useState, useEffect } from 'react';
import { useAuth } from '@/contexts/auth-context';
import { Card, CardContent, CardDescription, CardHeader, CardTitle, CardFooter } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { CheckCircle, Sparkles, Crown, User, LogOut } from 'lucide-react';
import { useRouter } from 'next/navigation';
import { useForm, type SubmitHandler } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { useToast } from '@/hooks/use-toast';
import { <PERSON>, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';

// --- SCHEMA VALIDATION ---
const profileEditSchema = z.object({
  displayName: z.string().min(2, "El nombre es requerido."),
  age: z.coerce.number().min(1, "La edad es requerida.").max(120, "Edad inválida."),
  therapistGender: z.enum(['female', 'male'], { required_error: "Por favor, elige un terapeuta." }),
  mentalHealthHistory: z.string().optional(),
  preferences: z.string().optional(),
});
type ProfileEditFormData = z.infer<typeof profileEditSchema>;

// --- FEATURES LISTS ---
const freeFeatures = [
    'Seguimiento de ánimo completo',
    '5 mensajes diarios con la IA',
    'Recomendaciones generales',
    'Acceso a 3 artículos al mes',
    'Acceso a la red de doctores',
];

const premiumFeatures = [
  'Chat ilimitado con la IA',
  'Recomendaciones personalizadas',
  'Seguimiento de ánimo avanzado',
  'Acceso a todos los artículos',
  'Soporte prioritario',
];


export default function ProfilePage() {
  const { user, userProfile, signOut, saveUserProfile, updateUserAuthProfile } = useAuth();
  const router = useRouter();
  const { toast } = useToast();
  const [isSubmitting, setIsSubmitting] = useState(false);

  const subscription = userProfile?.subscription as { status: string; type: string; expires_date: string; } | undefined;
  const isSubscribed = subscription?.status === 'active';

  const form = useForm<ProfileEditFormData>({
    resolver: zodResolver(profileEditSchema),
    defaultValues: {
      displayName: '',
      age: undefined,
      therapistGender: 'female',
      mentalHealthHistory: '',
      preferences: '',
    },
  });

  useEffect(() => {
    if (userProfile) {
      form.reset({
        displayName: userProfile.displayName || '',
        age: userProfile.age || undefined,
        therapistGender: userProfile.therapistGender || 'female',
        mentalHealthHistory: userProfile.mentalHealthHistory || '',
        preferences: userProfile.preferences || '',
      });
    }
  }, [userProfile, form]);

  const onProfileUpdate: SubmitHandler<ProfileEditFormData> = async (data) => {
    if (!user || !userProfile) return;
    setIsSubmitting(true);
    try {
      if(data.displayName !== user.displayName) {
        await updateUserAuthProfile({ displayName: data.displayName });
      }

      const updatedProfileData = {
        ...userProfile,
        displayName: data.displayName,
        age: data.age,
        therapistGender: data.therapistGender,
        mentalHealthHistory: data.mentalHealthHistory,
        preferences: data.preferences,
      };
      await saveUserProfile(user.uid, updatedProfileData);

      toast({ title: '¡Éxito!', description: 'Tu perfil ha sido actualizado.' });
    } catch (error) {
      toast({ title: 'Error', description: 'No se pudo actualizar tu perfil.', variant: 'destructive' });
    } finally {
      setIsSubmitting(false);
    }
  };

  const getInitials = (name?: string) => {
    if (!name) return '?';
    const names = name.split(' ');
    if (names.length > 1) {
      return `${names[0][0]}${names[names.length - 1][0]}`.toUpperCase();
    }
    return name.substring(0, 2).toUpperCase();
  };

  return (
    <div className="space-y-8 max-w-4xl mx-auto">
      <div className="flex items-center space-x-4">
        <Avatar className="h-20 w-20 border-2 border-primary">
          <AvatarImage src={user?.photoURL || ''} alt={userProfile?.displayName || ''} />
          <AvatarFallback className="text-3xl">{getInitials(userProfile?.displayName)}</AvatarFallback>
        </Avatar>
        <div>
          <h1 className="text-2xl font-bold">{userProfile?.displayName}</h1>
          <p className="text-muted-foreground">{user?.email}</p>
        </div>
      </div>

      <Card className={isSubscribed ? "border-primary/80 border-2 shadow-lg" : "bg-gradient-to-tr from-primary/5 via-background to-background"}>
        <CardHeader>
          <div className="flex items-center">
            {isSubscribed ? <Crown className="mr-3 h-7 w-7 text-primary" /> : <Sparkles className="mr-3 h-7 w-7 text-primary" />}
            <div>
              <CardTitle className="text-xl">{isSubscribed ? 'Suscripción Premium Activa' : 'Desbloquea Todo tu Potencial'}</CardTitle>
              <CardDescription>{isSubscribed && subscription ? `Plan ${subscription.type === 'annual' ? 'Anual' : 'Mensual'} - Vence el ${new Date(subscription.expires_date).toLocaleDateString()}` : 'Conviértete en premium para acceder a todas las funciones.'}</CardDescription>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          <h3 className="font-semibold mb-4">{isSubscribed ? 'Tu plan incluye:' : 'Tu plan gratuito incluye:'}</h3>
          <ul className="space-y-3">
            {(isSubscribed ? premiumFeatures : freeFeatures).map((feature) => (
              <li key={feature} className="flex items-center"><CheckCircle className="mr-3 h-5 w-5 text-green-500 flex-shrink-0" /><span>{feature}</span></li>
            ))}
          </ul>
        </CardContent>
        {!isSubscribed && (
          <CardFooter>
            <Button onClick={() => router.push('/subscribe')} className="w-full text-lg py-6 shadow-lg shadow-primary/20 hover:shadow-primary/30">Ver Planes Premium</Button>
          </CardFooter>
        )}
      </Card>

      <Card>
        <CardHeader>
          <CardTitle>Editar Perfil</CardTitle>
          <CardDescription>Mantén tus datos y preferencias actualizados.</CardDescription>
        </CardHeader>
        <CardContent>
          <Form {...form}>
            <form onSubmit={form.handleSubmit(onProfileUpdate)} className="space-y-6">
              <FormField control={form.control} name="displayName" render={({ field }) => (
                <FormItem><FormLabel>Nombre para Mostrar</FormLabel><FormControl><Input placeholder="Tu nombre" {...field} /></FormControl><FormMessage /></FormItem>
              )} />
              <FormField control={form.control} name="age" render={({ field }) => (
                <FormItem><FormLabel>Edad</FormLabel><FormControl><Input type="number" placeholder="Tu edad" {...field} value={field.value ?? ''} /></FormControl><FormMessage /></FormItem>
              )} />
              <FormField control={form.control} name="therapistGender" render={({ field }) => (
                <FormItem>
                  <FormLabel>Elige a tu Terapeuta</FormLabel>
                  <Select onValueChange={field.onChange} value={field.value}>
                    <FormControl><SelectTrigger><SelectValue placeholder="Selecciona un terapeuta" /></SelectTrigger></FormControl>
                    <SelectContent>
                      <SelectItem value="female">Psicóloga Aurora</SelectItem>
                      <SelectItem value="male">Psicólogo Alejandro</SelectItem>
                    </SelectContent>
                  </Select>
                  <FormMessage />
                </FormItem>
              )} />
              <FormField control={form.control} name="mentalHealthHistory" render={({ field }) => (
                <FormItem><FormLabel>Contexto de Bienestar Emocional</FormLabel><FormControl><Textarea placeholder="Describe brevemente tus experiencias o preocupaciones..." {...field} /></FormControl><FormMessage /></FormItem>
              )} />
              <FormField control={form.control} name="preferences" render={({ field }) => (
                <FormItem><FormLabel>Preferencias de Interacción</FormLabel><FormControl><Textarea placeholder="¿Qué tipo de apoyo buscas de la IA?" {...field} /></FormControl><FormMessage /></FormItem>
              )} />
              <CardFooter className="px-0 pt-6 flex justify-end">
                 <Button type="submit" disabled={isSubmitting}>{isSubmitting ? 'Guardando...' : 'Guardar Cambios'}</Button>
              </CardFooter>
            </form>
          </Form>
        </CardContent>
      </Card>
        
      <div className="text-center pt-4">
        <Button onClick={signOut} variant="ghost" className="text-muted-foreground hover:text-destructive"><LogOut className="mr-2 h-4 w-4" /> Salir de la Cuenta</Button>
      </div>
    </div>
  );
}
