
import { initializeApp, getApps, getApp } from 'firebase/app';
import { getAuth, connectAuthEmulator } from 'firebase/auth';
import { getFirestore, connectFirestoreEmulator } from 'firebase/firestore';

const firebaseConfig = {
  apiKey: process.env.NEXT_PUBLIC_FIREBASE_API_KEY,
  authDomain: process.env.NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN,
  projectId: process.env.NEXT_PUBLIC_FIREBASE_PROJECT_ID,
  storageBucket: process.env.NEXT_PUBLIC_FIREBASE_STORAGE_BUCKET,
  messagingSenderId: process.env.NEXT_PUBLIC_FIREBASE_MESSAGING_SENDER_ID,
  appId: process.env.NEXT_PUBLIC_FIREBASE_APP_ID,
};

// Imprimir la configuración de Firebase en la consola del navegador para depuración.
// Esto solo se ejecutará en el lado del cliente.
// if (typeof window !== 'undefined') {
//   console.log("Firebase config being used by the app:", firebaseConfig);
// }

const app = !getApps().length ? initializeApp(firebaseConfig) : getApp();
const auth = getAuth(app);
const db = getFirestore(app);

if (process.env.NEXT_PUBLIC_USE_FIREBASE_EMULATOR === 'true') {
  if (typeof window !== 'undefined') { // Ensure this only runs on client
    // Check if already connected to avoid re-connecting error
    // @ts-ignore auth.emulatorConfig is not in type definition
    if (!auth.emulatorConfig) {
      connectAuthEmulator(auth, 'http://localhost:9099', { disableWarnings: true });
    }
    // @ts-ignore db._settings is not in type definition for direct host/port check
    // A more robust check might involve trying a simple Firestore operation or checking internal flags if available
    // For now, assume if one emulator is configured, others should be too, or rely on Firebase SDK's internal state.
     try {
      connectFirestoreEmulator(db, 'localhost', 8080);
    } catch (e) {
      // Firestore emulator might already be connected
      // console.warn("Firestore emulator connection error (might be already connected):", e);
    }
  }
}

export { app, auth, db };

