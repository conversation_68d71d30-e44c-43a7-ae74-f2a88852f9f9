
'use client';

import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Award, Construction } from 'lucide-react';

export default function AchievementsPage() {
  return (
    <div className="space-y-8">
      <Card className="shadow-lg text-center">
        <CardHeader>
          <div className="mx-auto mb-4 flex h-16 w-16 items-center justify-center rounded-full bg-primary/10 text-primary">
            <Award className="h-10 w-10" />
          </div>
          <CardTitle className="text-2xl font-headline">
            Mis Logros y Progreso
          </CardTitle>
          <CardDescription>
            ¡Esta sección está en camino! Aquí podrás ver tus avances y reconocimientos.
          </CardDescription>
        </CardHeader>
        <CardContent className="prose prose-sm sm:prose lg:prose-lg xl:prose-xl max-w-none dark:prose-invert text-foreground space-y-4 py-12">
          <div className="flex flex-col items-center justify-center text-muted-foreground">
            <Construction className="h-24 w-24 mb-6" />
            <h2 className="text-xl font-semibold">Página en Construcción</h2>
            <p className="mt-2 max-w-md">
              Estamos trabajando para traerte una increíble sección de logros y gamificación.
              Aquí podrás seguir tu progreso, desbloquear insignias y mucho más.
            </p>
            <p className="mt-1 text-sm">¡Vuelve pronto!</p>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
