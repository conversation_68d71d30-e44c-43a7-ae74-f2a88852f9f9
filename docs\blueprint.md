# **App Name**: MindBloom

## Core Features:

- Autenticación: Autenticación con Firebase para permitir a los usuarios iniciar sesión de forma anónima.
- Interfaz de Chat: Interfaz de chat en tiempo real donde los usuarios pueden interactuar con el chatbot de IA.
- Chatbot de IA: Chatbot de IA que utiliza Gemini AI para proporcionar apoyo psicológico instantáneo con técnicas terapéuticas como la Terapia Cognitivo-Conductual (TCC) y mindfulness. El chatbot de IA tendrá un avatar visual que se asemeja a un médico amigable. El chatbot utilizará razonamiento para decidir cuándo o si incorporar cierta información en su respuesta.
- Seguimiento del Estado de Ánimo: Función de seguimiento del estado de ánimo que permite a los usuarios registrar su estado de ánimo diariamente, rastrear su historial de estado de ánimo y visualizarlo en un gráfico de líneas.
- Recomendaciones Personalizadas: Basado en las entradas de estado de ánimo del usuario, la IA proporcionará recomendaciones terapéuticas o de autoayuda personalizadas, basadas en los principios de la Terapia Cognitivo-Conductual (TCC), la psicología positiva o el mindfulness. La IA utilizará razonamiento para decidir cuándo o si incorporar cierta información en su respuesta.
- Artículos Dinámicos: Los usuarios pueden generar artículos sobre temas relacionados con la salud mental. La IA utilizará razonamiento para decidir cuándo o si incorporar cierta información en su respuesta.
- Descargo de Responsabilidad: Muestra un descargo de responsabilidad sobre el uso de la IA en la aplicación para el apoyo psicológico. Indica la importancia de la ayuda profesional y explica claramente el papel de la IA como una herramienta de apoyo únicamente.
- Pantalla de Inicio de Sesión: Pantalla inicial para que los usuarios creen una cuenta o inicien sesión.
- Creación de Perfil: Un formulario para que los nuevos usuarios ingresen información personal (edad, género, historial de salud mental, preferencias, etc.). Estos datos se utilizarán para personalizar las respuestas y recomendaciones de la IA. La IA utilizará razonamiento para decidir cuándo o si incorporar cierta información en su respuesta.
- Red de Doctores: Una pantalla que muestra una lista de los doctores y psicólogos que colaboran con la aplicación.

## Style Guidelines:

- Azul suave y apagado (#64B5F6) para promover la calma y la confianza.
- Azul muy claro (#F0F8FF), casi blanco, para dar una sensación de limpieza y paz.
- Lavanda suave (#B39DDB) como acento secundario para sugerir equilibrio y salud emocional.
- Fuente del encabezado: 'Literata' (serif) para una sensación intelectual, ligeramente formal.
- Fuente del cuerpo: 'Inter' (sans-serif) para un estilo moderno, neutral y legible.
- Fuente del código: 'Source Code Pro' para mostrar fragmentos de código.
- Iconos simples y modernos de Lucide, relacionados con el bienestar mental, el chat, los estados de ánimo y la educación.
- Diseño limpio y fácil de usar con un enfoque en la navegación y la legibilidad fáciles.
- Animaciones y transiciones sutiles para mejorar la experiencia del usuario y proporcionar una retroalimentación suave.