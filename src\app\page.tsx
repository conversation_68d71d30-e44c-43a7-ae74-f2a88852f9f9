
'use client';

import { useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { useAuth } from '@/contexts/auth-context';
import { Brain } from 'lucide-react';

export default function HomePage() {
  const { user, initialLoading } = useAuth();
  const router = useRouter();

  useEffect(() => {
    // Esperar a que se determine el estado de autenticación
    if (initialLoading) {
      return;
    }

    // Si hay un usuario, redirigir al chat, si no, al login.
    if (user) {
      router.replace('/dashboard');
    } else {
      router.replace('/login');
    }
  }, [user, initialLoading, router]);

  // Muestra un loader a pantalla completa mientras se decide a dónde redirigir.
  return (
    <div className="flex h-screen w-screen items-center justify-center bg-background">
      <Brain className="h-16 w-16 animate-pulse text-primary" />
    </div>
  );
}
