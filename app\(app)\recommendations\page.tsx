
'use client';

import React, { useState, useEffect } from 'react';
import { useAuth } from '@/contexts/auth-context';
import type { MoodEntry } from '@/types';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Lightbulb, AlertCircle, Sparkles, Star } from 'lucide-react';
import { personalizedRecommendations, type PersonalizedRecommendationsInput, type PersonalizedRecommendationsOutput } from '@/ai/flows/personalized-recommendations';
import { db } from '@/lib/firebase';
import { collection, query, orderBy, limit, getDocs, Timestamp } from 'firebase/firestore';
import { useToast } from '@/hooks/use-toast';
import { Skeleton } from '@/components/ui/skeleton';
import { format } from 'date-fns';
import { es } from 'date-fns/locale';
import Link from 'next/link';

export default function RecommendationsPage() {
  const { user, userProfile } = useAuth();
  const { toast } = useToast();
  const [result, setResult] = useState<PersonalizedRecommendationsOutput | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [moodEntriesSummary, setMoodEntriesSummary] = useState<string>('');
  
  const isSubscribed = userProfile?.subscription?.status === 'active';

  const fetchMoodEntries = async (): Promise<MoodEntry[]> => {
    if (!user) return [];
    const q = query(
      collection(db, `users/${user.uid}/moodEntries`),
      orderBy('createdAt', 'desc'),
      limit(10)
    );
    const querySnapshot = await getDocs(q);
    const entries: MoodEntry[] = [];
    querySnapshot.forEach(doc => {
      const data = doc.data();
      entries.push({ 
        id: doc.id, 
        ...data,
        date: (data.date as Timestamp)?.toDate ? (data.date as Timestamp).toDate().toISOString() : data.date as string,
        createdAt: (data.createdAt as Timestamp).toDate() 
      } as MoodEntry);
    });
    return entries;
  };

  const generateRecommendation = async () => {
    if (!user || !userProfile) {
      toast({ title: "Error", description: "Debes iniciar sesión.", variant: "destructive" });
      return;
    }
    setIsLoading(true);
    setResult(null);

    try {
      const moodEntries = await fetchMoodEntries();
      
      // MÉTODO A PRUEBA DE ERRORES PARA CONSTRUIR LA CADENA
      let entriesText = "";
      for (const e of moodEntries) {
        const formattedDate = format(new Date(e.date), "PPP", { locale: es });
        const descriptionPart = e.description ? `: ${e.description}` : '.';
        entriesText += `En ${formattedDate}, el ánimo fue ${e.mood}${descriptionPart}
`;
      }
      entriesText = entriesText.trim();
      
      setMoodEntriesSummary(entriesText);

      const input: PersonalizedRecommendationsInput = {
        uid: user.uid,
        moodEntries: entriesText,
        userPreferences: userProfile?.preferences || '',
      };

      const recommendationResult = await personalizedRecommendations(input);
      setResult(recommendationResult);
      
      const toastTitle = recommendationResult.isPersonalized ? "¡Recomendación Personalizada!" : "Aquí tienes un consejo";
      toast({ title: toastTitle });

    } catch (error) {
      console.error("Error generando recomendación:", error);
      toast({ title: "Error", description: "No se pudo generar la recomendación.", variant: "destructive" });
    } finally {
      setIsLoading(false);
    }
  };
  
  return (
    <div className="space-y-8">
      <Card className="shadow-lg">
        <CardHeader>
          <CardTitle className="text-2xl font-headline flex items-center"><Lightbulb className="mr-2 h-7 w-7 text-primary" /> Recomendaciones</CardTitle>
          <CardDescription>Obtén consejos de autoayuda impulsados por IA.</CardDescription>
        </CardHeader>
        <CardContent className="text-center">
          <Button onClick={generateRecommendation} disabled={isLoading} size="lg" className="my-4">
            <Sparkles className="mr-2 h-5 w-5" />
            {isLoading ? 'Generando...' : 'Obtener Recomendación'}
          </Button>
        </CardContent>
      </Card>

      {isLoading && (
        <Card className="shadow-md">
          <CardHeader>
            <Skeleton className="h-6 w-3/4" />
          </CardHeader>
          <CardContent className="space-y-2">
            <Skeleton className="h-4 w-full" />
            <Skeleton className="h-4 w-full" />
            <Skeleton className="h-4 w-5/6" />
          </CardContent>
        </Card>
      )}

      {result && !isLoading && (
        <Card className="shadow-md bg-accent/20 border-accent">
          <CardHeader>
            <CardTitle className="text-xl font-headline text-accent-foreground flex items-center">
              {result.isPersonalized ? <Star className="mr-2 h-6 w-6 text-yellow-400" /> : <Sparkles className="mr-2 h-6 w-6" />}
              {result.isPersonalized ? 'Una Recomendación Personalizada Para Ti:' : 'Una Idea Para Ti:'}
            </CardTitle>
             {!isSubscribed && !result.isPersonalized && (
                <CardDescription className="text-xs !mt-2">
                    <Link href="/subscribe" className="underline hover:text-primary">Suscríbete a Premium</Link> para recibir recomendaciones basadas en tu historial de ánimo.
                </CardDescription>
            )}
          </CardHeader>
          <CardContent>
            <p className="text-accent-foreground/90 whitespace-pre-wrap">{result.recommendation}</p>
            {moodEntriesSummary && result.isPersonalized && (
               <details className="mt-4 text-sm">
                <summary className="cursor-pointer text-muted-foreground hover:text-foreground">Basado en tus entradas de ánimo recientes</summary>
                <p className="mt-2 p-2 border rounded bg-background text-muted-foreground whitespace-pre-wrap">{moodEntriesSummary}</p>
              </details>
            )}
          </CardContent>
        </Card>
      )}

      {!result && !isLoading && (
         <Card className="shadow-md border-dashed">
          <CardContent className="p-6 text-center">
             <AlertCircle className="mx-auto h-12 w-12 text-muted-foreground mb-2" />
            <p className="text-muted-foreground">Haz clic en el botón de arriba para generar una recomendación.</p>
             <p className="text-xs text-muted-foreground mt-1">Asegúrate de tener algunas entradas de ánimo recientes para obtener mejores resultados.</p>
          </CardContent>
        </Card>
      )}
    </div>
  );
}
