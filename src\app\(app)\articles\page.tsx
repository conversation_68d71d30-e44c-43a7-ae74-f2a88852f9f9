
'use client';

import React, { useState, useEffect, useMemo } from 'react';
import { useForm, type SubmitHandler } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle } from '@/components/ui/alert-dialog';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogFooter, DialogClose } from '@/components/ui/dialog';
import { Scroll<PERSON><PERSON> } from '@/components/ui/scroll-area';
import { BookOpen, Feather, AlertCircle, Save, Loader2, CheckCircle, Trash2, Eye, Image as ImageIcon, Sparkles, Share2 } from 'lucide-react';
import type { Article as ArticleType, GenerateArticleOutput } from '@/types';
import { generateArticle as generateArticleFlow } from '@/ai/flows/generate-article';
import { useToast } from '@/hooks/use-toast';
import { Skeleton } from '@/components/ui/skeleton';
import { useAuth } from '@/contexts/auth-context';
import { db } from '@/lib/firebase';
import { collection, addDoc, serverTimestamp, query, onSnapshot, orderBy, doc, deleteDoc, Timestamp } from 'firebase/firestore';
import { format } from 'date-fns';
import { es } from 'date-fns/locale';
import Image from 'next/image';
import { useRouter } from 'next/navigation';

const articleRequestSchema = z.object({
  topic: z.string().min(3, "El tema debe tener al menos 3 caracteres."),
});
type ArticleRequestFormData = z.infer<typeof articleRequestSchema>;

export default function ArticlesPage() {
  const { toast } = useToast();
  const { user } = useAuth();
  const router = useRouter();
  
  const [generatedArticle, setGeneratedArticle] = useState<(GenerateArticleOutput & { topic: string }) | null>(null);
  const [isGenerating, setIsGenerating] = useState(false);
  const [limitReached, setLimitReached] = useState(false);
  const [isSaving, setIsSaving] = useState(false);
  
  const [savedArticles, setSavedArticles] = useState<ArticleType[]>([]);
  const [isLoadingSaved, setIsLoadingSaved] = useState(true);
  const [articleToDelete, setArticleToDelete] = useState<ArticleType | null>(null);
  const [viewingArticle, setViewingArticle] = useState<ArticleType | null>(null);

  const form = useForm<ArticleRequestFormData>({
    resolver: zodResolver(articleRequestSchema),
    defaultValues: { topic: '' },
  });
  
  const isGeneratedArticleSaved = useMemo(() => {
    if (!generatedArticle) return false;
    return savedArticles.some(saved => saved.title === generatedArticle.title && saved.topic === generatedArticle.topic);
  }, [generatedArticle, savedArticles]);

  useEffect(() => {
    if (!user) {
      setIsLoadingSaved(false);
      setSavedArticles([]);
      return;
    }

    const articlesRef = collection(db, `users/${user.uid}/savedArticles`);
    const q = query(articlesRef, orderBy('savedAt', 'desc'));

    const unsubscribe = onSnapshot(q, (snapshot) => {
      const articlesData = snapshot.docs.map(docSnapshot => {
        const data = docSnapshot.data();
        return {
          id: docSnapshot.id,
          ...data,
        } as ArticleType;
      });
      setSavedArticles(articlesData);
      setIsLoadingSaved(false);
    }, (error) => {
      console.error("Error loading saved articles:", error);
      toast({ title: "Error", description: "No se pudieron cargar los artículos guardados.", variant: "destructive" });
      setIsLoadingSaved(false);
    });

    return () => unsubscribe();
  }, [user, toast]);


  const onSubmit: SubmitHandler<ArticleRequestFormData> = async (data) => {
    if (!user) return;
    setIsGenerating(true);
    setGeneratedArticle(null);
    setLimitReached(false);
    
    try {
      const result = await generateArticleFlow({ uid: user.uid, topic: data.topic });
      setGeneratedArticle({ ...result, topic: data.topic });
    } catch (error: any) {
      if (error.message === 'ARTICLE_LIMIT_REACHED') {
        setLimitReached(true);
      } else {
        toast({ title: "Error al generar artículo", variant: "destructive" });
      }
    } finally {
      setIsGenerating(false);
    }
  };

  const handleSaveArticle = async () => {
    if (!generatedArticle || !user) return;
    setIsSaving(true);
    try {
      await addDoc(collection(db, `users/${user.uid}/savedArticles`), {
        title: generatedArticle.title,
        content: generatedArticle.content,
        topic: generatedArticle.topic,
        savedAt: serverTimestamp(),
      });
      toast({ title: "¡Artículo Guardado!" });
    } catch (error) {
      toast({ title: "Error al guardar", variant: "destructive" });
    } finally {
      setIsSaving(false);
    }
  };

  const handleDeleteArticle = async (articleId: string) => {
    if (!user) return;
    try {
      await deleteDoc(doc(db, `users/${user.uid}/savedArticles`, articleId));
      toast({ title: "Artículo Eliminado" });
      setArticleToDelete(null);
    } catch (error) {
       toast({ title: "Error al eliminar", variant: "destructive" });
    }
  };
  
  const handleShare = async (article: GenerateArticleOutput) => {
    const shareData = {
      title: `Artículo de Mente en Calma: ${article.title}`,
      text: `${article.title}

${article.content}`,
      url: window.location.href,
    };
    try {
      if (navigator.share) await navigator.share(shareData);
      else {
        await navigator.clipboard.writeText(shareData.text);
        toast({ title: "Artículo copiado al portapapeles" });
      }
    } catch (err) {
       toast({ title: "Error al compartir", variant: "destructive" });
    }
  };

  const formatDate = (timestamp?: Timestamp | Date): string => {
    if (!timestamp) return 'Fecha desconocida';
    const date = timestamp instanceof Timestamp ? timestamp.toDate() : timestamp;
    return format(date, 'd MMM yyyy', { locale: es });
  };

  return (
    <div className="space-y-8">
      {/* SECTION: GENERADOR DE ARTÍCULOS */}
      <Card className="shadow-lg">
        <CardHeader>
          <CardTitle className="text-2xl font-headline flex items-center"><Feather className="mr-2 h-7 w-7 text-primary" /> Generador de Artículos de IA</CardTitle>
          <CardDescription>Genera artículos breves y accionables sobre temas de salud mental.</CardDescription>
        </CardHeader>
        <CardContent>
          {limitReached ? (
            <div className="text-center p-6 bg-muted rounded-lg">
                <Sparkles className="mx-auto h-8 w-8 text-primary mb-2" />
                <h3 className="font-bold text-lg">Has alcanzado tu límite de artículos</h3>
                <p className="text-sm text-muted-foreground mb-4">Actualiza a Premium para generar contenido ilimitado.</p>
                <Button onClick={() => router.push('/subscribe')}>Ver Planes Premium</Button>
            </div>
          ) : (
            <Form {...form}><form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
              <FormField control={form.control} name="topic" render={({ field }) => (
                <FormItem><FormLabel>Tema del Artículo</FormLabel><FormControl><Input placeholder="ej., 'Manejo del Estrés', 'Superar la Ansiedad Social'" {...field} /></FormControl><FormMessage /></FormItem>
              )} />
              <Button type="submit" disabled={isGenerating}>{isGenerating ? <><Loader2 className="mr-2 h-4 w-4 animate-spin" />Generando...</> : 'Generar Artículo'}</Button>
            </form></Form>
          )}
        </CardContent>
      </Card>

      {/* SECTION: ARTÍCULO GENERADO */}
      {isGenerating && <Skeleton className="h-64 w-full" />}
      {generatedArticle && !isGenerating && (
        <Card>
          {generatedArticle.imageUrl && <div className="relative h-56 w-full"><Image src={generatedArticle.imageUrl} alt={generatedArticle.title} fill sizes="100vw" className="object-cover rounded-t-lg" /></div>}
          <CardHeader><CardTitle className="text-3xl font-headline text-primary">{generatedArticle.title}</CardTitle></CardHeader>
          <CardContent><div className="prose prose-sm sm:prose-lg max-w-none dark:prose-invert text-foreground whitespace-pre-wrap">{generatedArticle.content}</div></CardContent>
          <CardFooter className="flex justify-between">
            <Button onClick={handleSaveArticle} disabled={isSaving || isGeneratedArticleSaved}>
              {isSaving ? <Loader2 className="mr-2 h-4 w-4 animate-spin" /> : isGeneratedArticleSaved ? <CheckCircle className="mr-2 h-4 w-4" /> : <Save className="mr-2 h-4 w-4" />}
              {isGeneratedArticleSaved ? 'Guardado' : 'Guardar Artículo'}
            </Button>
            <Button onClick={() => handleShare(generatedArticle)} variant="outline"><Share2 className="mr-2 h-4 w-4" />Compartir</Button>
          </CardFooter>
        </Card>
      )}

      {/* SECTION: MIS ARTÍCULOS GUARDADOS */}
      <div className="space-y-4">
        <h2 className="text-2xl font-headline font-semibold flex items-center"><BookOpen className="mr-2 h-7 w-7 text-primary" /> Mis Artículos Guardados</h2>
        {isLoadingSaved && <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">{[1,2,3].map(i => <Skeleton key={i} className="h-40 w-full" />)}</div>}
        {!isLoadingSaved && savedArticles.length === 0 && <p className="text-muted-foreground">Aquí aparecerán los artículos que guardes.</p>}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {savedArticles.map(article => (
            <Card key={article.id} className="flex flex-col"><CardHeader><CardTitle className="line-clamp-2">{article.title}</CardTitle><CardDescription>{formatDate(article.savedAt as Timestamp)}</CardDescription></CardHeader>
              <CardContent className="flex-grow"><p className="line-clamp-3 text-sm text-muted-foreground">{article.content}</p></CardContent>
              <CardFooter className="flex justify-between">
                <Button variant="ghost" onClick={() => setViewingArticle(article)}><Eye className="mr-2 h-4 w-4"/>Ver</Button>
                <Button variant="ghost" size="icon" onClick={() => setArticleToDelete(article)}><Trash2 className="h-4 w-4 text-destructive"/></Button>
              </CardFooter>
            </Card>
          ))}
        </div>
      </div>
      
      {/* DIALOGS */}
      <Dialog open={!!viewingArticle} onOpenChange={isOpen => !isOpen && setViewingArticle(null)}>
        <DialogContent className="sm:max-w-2xl"><DialogHeader><DialogTitle>{viewingArticle?.title}</DialogTitle></DialogHeader>
          <ScrollArea className="max-h-[60vh] my-4 pr-6">
            <div className="prose dark:prose-invert text-gray-200 whitespace-pre-wrap">{viewingArticle?.content}</div>
          </ScrollArea>
        </DialogContent>
      </Dialog>
      <AlertDialog open={!!articleToDelete} onOpenChange={isOpen => !isOpen && setArticleToDelete(null)}>
        <AlertDialogContent><AlertDialogHeader><AlertDialogTitle>¿Confirmar eliminación?</AlertDialogTitle><AlertDialogDescription>"{articleToDelete?.title}" será eliminado permanentemente.</AlertDialogDescription></AlertDialogHeader>
          <AlertDialogFooter><AlertDialogCancel>Cancelar</AlertDialogCancel><AlertDialogAction onClick={() => handleDeleteArticle(articleToDelete!.id)}>Eliminar</AlertDialogAction></AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  );
}
