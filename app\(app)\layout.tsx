
'use client';

import React, { useEffect } from 'react';
import { useRouter, usePathname } from 'next/navigation';
import { useAuth } from '@/contexts/auth-context';
import { Sidebar, SidebarProvider, SidebarInset, SidebarTrigger, SidebarHeader, SidebarContent, SidebarMenu, SidebarMenuItem, SidebarMenuButton, SidebarFooter } from '@/components/ui/sidebar';
import { Button } from '@/components/ui/button';
import { LayoutDashboard, MessageCircle, BarChart3, Lightbulb, BookOpen, Users, ShieldAlert, LogOut, Brain, UserCircle as UserIconFallback, Gamepad2 } from 'lucide-react';
import Link from 'next/link';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { TooltipProvider } from '@/components/ui/tooltip';

export default function AppLayout({ children }: { children: React.ReactNode }) {
  const { user, userProfile, initialLoading, signOut } = useAuth();
  const router = useRouter();
  const pathname = usePathname();

  useEffect(() => {
    if (initialLoading) return;

    if (!user) {
      router.replace('/login');
      return;
    }
    
    if (user && !userProfile && pathname !== '/create-profile') {
      router.replace('/create-profile');
    }
  }, [user, userProfile, initialLoading, router, pathname]);

  if (initialLoading || !user) {
    return (
      <div className="flex h-screen w-screen items-center justify-center bg-background">
        <Brain className="h-16 w-16 animate-pulse text-primary" />
        <p className="ml-4 text-lg">Cargando...</p>
      </div>
    );
  }
  
  if (pathname === '/create-profile') {
    return <>{children}</>;
  }
  
  if (!userProfile) {
    return (
        <div className="flex h-screen w-screen items-center justify-center bg-background">
            <Brain className="h-16 w-16 animate-pulse text-primary" />
            <p className="ml-4 text-lg">Cargando perfil...</p>
        </div>
    );
  }

  const menuItems = [
    { href: '/dashboard', icon: LayoutDashboard, label: 'Inicio' },
    { href: '/chat', icon: MessageCircle, label: 'Chat IA' },
    { href: '/mood-tracker', icon: BarChart3, label: 'Seguimiento de Ánimo' },
    { href: '/recommendations', icon: Lightbulb, label: 'Recomendaciones' },
    { href: '/articles', icon: BookOpen, label: 'Artículos' },
    { href: '/doctors', icon: Users, label: 'Red de Doctores' },
    { href: '/disclaimer', icon: ShieldAlert, label: 'Descargo de Responsabilidad' },
  ];

  const getInitials = (name?: string) => {
    if (!name) return '?';
    const names = name.split(' ');
    return names.length > 1 ? `${names[0][0]}${names[names.length - 1][0]}`.toUpperCase() : name.substring(0, 2).toUpperCase();
  };

  return (
    <TooltipProvider>
      <SidebarProvider defaultOpen>
        <Sidebar collapsible="icon" className="border-r bg-sidebar text-sidebar-foreground">
          <SidebarHeader className="p-4 flex justify-center">
            <Link href="/dashboard"><Brain className="h-8 w-8 text-primary" /></Link>
          </SidebarHeader>
          <SidebarContent>
            <SidebarMenu>
              {menuItems.map((item) => (
                <SidebarMenuItem key={item.href}>
                  <Link href={item.href} legacyBehavior passHref>
                    <SidebarMenuButton tooltip={item.label} isActive={pathname.startsWith(item.href)}>
                      <item.icon className="h-5 w-5" />
                      <span>{item.label}</span>
                    </SidebarMenuButton>
                  </Link>
                </SidebarMenuItem>
              ))}
            </SidebarMenu>
          </SidebarContent>
          <SidebarFooter className="p-2 border-t border-border">
            <div className="flex flex-col items-center gap-2 w-full">
              <Link href="/profile" className="w-full">
                <div className="flex items-center p-2 rounded-md hover:bg-accent/50">
                    <Avatar className="h-10 w-10 border-2 border-primary">
                        <AvatarImage src={user.photoURL || ''} alt={userProfile.displayName || ''} />
                        <AvatarFallback>{getInitials(userProfile.displayName)}</AvatarFallback>
                    </Avatar>
                    <div className="ml-3 group-data-[collapsible=icon]:hidden">
                        <p className="text-sm font-semibold leading-tight">{userProfile.displayName}</p>
                        <p className="text-xs text-muted-foreground leading-tight">{user.email}</p>
                    </div>
                </div>
               </Link>
              <Button variant="ghost" onClick={signOut} className="w-full justify-center group-data-[collapsible=icon]:w-auto">
                <LogOut className="h-5 w-5" />
                <span className="group-data-[collapsible=icon]:hidden ml-2">Cerrar Sesión</span>
              </Button>
            </div>
          </SidebarFooter>
        </Sidebar>
        <SidebarInset>
          <header className="sticky top-0 z-10 flex h-14 items-center justify-end border-b border-border bg-background/80 px-4 backdrop-blur-md">
            <div className="flex items-center gap-3">
              <Button variant="ghost" size="icon" aria-label="Gamificación" className="h-9 w-9 text-primary hover:bg-primary/10">
                <Gamepad2 className="h-5 w-5" />
              </Button>
              <Link href="/profile" passHref>
                <Avatar className="h-9 w-9 cursor-pointer border-2 border-transparent hover:border-primary">
                  <AvatarImage src={user.photoURL || ''} alt={userProfile.displayName || ''} />
                  <AvatarFallback>{getInitials(userProfile.displayName)}</AvatarFallback>
                </Avatar>
              </Link>
            </div>
          </header>
          <main className="flex-1 p-4 md:p-6 lg:p-8 overflow-auto h-[calc(100vh-56px)]">
            {children}
          </main>
        </SidebarInset>
      </SidebarProvider>
    </TooltipProvider>
  );
}
