
'use client';

import type { User } from 'firebase/auth';
import React, { createContext, useContext, useEffect, useState, useCallback } from 'react';
import { auth, db } from '@/lib/firebase';
import { 
  onAuthStateChanged, 
  signOut as firebaseSignOut,
  GoogleAuthProvider,
  signInWithPopup,
  createUserWithEmailAndPassword,
  signInWithEmailAndPassword,
  updateProfile as firebaseUpdateProfile,
} from 'firebase/auth';
import { doc, getDoc, setDoc, serverTimestamp } from 'firebase/firestore';
import type { UserProfile } from '@/types';

interface AuthContextType {
  user: User | null;
  userProfile: UserProfile | null;
  loading: boolean;
  initialLoading: boolean;
  authError: string | null;
  signInWithGoogle: () => Promise<User | null>;
  signUpWithEmail: (email: string, pass: string, displayName?: string) => Promise<User | null>;
  signInWithEmail: (email: string, pass: string) => Promise<User | null>;
  signOut: () => Promise<void>;
  saveUserProfile: (uid: string, profileData: Partial<UserProfile>) => Promise<void>;
  updateUserAuthProfile: (data: { displayName?: string; photoURL?: string; }) => Promise<void>;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export const AuthProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [user, setUser] = useState<User | null>(null);
  const [userProfile, setUserProfile] = useState<UserProfile | null>(null);
  const [loading, setLoading] = useState(false); 
  const [initialLoading, setInitialLoading] = useState(true); 
  const [authError, setAuthError] = useState<string | null>(null);

  const fetchUserProfile = useCallback(async (uid: string): Promise<UserProfile | null> => {
    const userDocRef = doc(db, 'users', uid);
    const userDoc = await getDoc(userDocRef);
    return userDoc.exists() ? userDoc.data() as UserProfile : null;
  }, []);

  useEffect(() => {
    const unsubscribe = onAuthStateChanged(auth, async (firebaseUser) => {
      setLoading(true);
      if (firebaseUser) {
        if (firebaseUser.uid !== user?.uid) {
          const profile = await fetchUserProfile(firebaseUser.uid);
          setUser(firebaseUser);
          setUserProfile(profile);
        }
      } else {
        setUser(null);
        setUserProfile(null);
      }
      setInitialLoading(false);
      setLoading(false);
    });
    return () => unsubscribe();
  }, [user?.uid, fetchUserProfile]);

  const signInWithGoogle = async (): Promise<User | null> => {
    setLoading(true);
    setAuthError(null);
    try {
      const provider = new GoogleAuthProvider();
      const result = await signInWithPopup(auth, provider);
      return result.user;
    } catch (error: any) {
      if (error.code !== 'auth/popup-closed-by-user') {
        setAuthError("No se pudo iniciar sesión con Google.");
      }
      return null;
    } finally {
      setLoading(false);
    }
  };
  
  const updateUserAuthProfile = async (data: { displayName?: string; photoURL?: string; }) => {
    if (!auth.currentUser) return;
    await firebaseUpdateProfile(auth.currentUser, data);
    setUser({ ...auth.currentUser });
  };
  
  const saveUserProfile = async (uid: string, profileData: Partial<UserProfile>) => {
    setLoading(true);
    const userDocRef = doc(db, 'users', uid);
    try {
      await setDoc(userDocRef, { ...profileData, updatedAt: serverTimestamp() }, { merge: true });
      setUserProfile(prev => ({ ...prev, ...profileData } as UserProfile));
    } catch(error) {
        setAuthError("No se pudo guardar el perfil.");
    } finally {
        setLoading(false);
    }
  };

  const signUpWithEmail = async (email: string, pass: string, displayName?: string) => {
    setLoading(true);
    setAuthError(null);
    try {
        const userCredential = await createUserWithEmailAndPassword(auth, email, pass);
        if (displayName) {
            await firebaseUpdateProfile(userCredential.user, { displayName });
        }
        return userCredential.user;
    } catch(error) {
        setAuthError("Error al crear la cuenta.");
        return null;
    } finally {
        setLoading(false);
    }
  };

  const signInWithEmail = async (email: string, pass: string) => {
     setLoading(true);
     setAuthError(null);
     try {
        const result = await signInWithEmailAndPassword(auth, email, pass);
        return result.user;
     } catch(error) {
        setAuthError("Correo o contraseña incorrectos.");
        return null;
     } finally {
        setLoading(false);
     }
  };

  const signOut = async () => {
    await firebaseSignOut(auth);
    setUser(null);
    setUserProfile(null);
  };

  return (
    <AuthContext.Provider value={{ user, userProfile, loading, initialLoading, authError, signInWithGoogle, signUpWithEmail, signInWithEmail, signOut, saveUserProfile, updateUserAuthProfile }}>
      {children}
    </AuthContext.Provider>
  );
};

export const useAuth = (): AuthContextType => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth debe usarse dentro de un AuthProvider');
  }
  return context;
};
