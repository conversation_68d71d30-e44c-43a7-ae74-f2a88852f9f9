
'use client';

import React, { useEffect, useState } from 'react';
import { useRouter } from 'next/navigation';
import { useAuth } from '@/contexts/auth-context';
import { <PERSON>, CalendarCheck, Lightbulb, Loader2, Sparkles } from 'lucide-react';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Textarea } from '@/components/ui/textarea';
import { generateDailyTip, type GenerateDailyTipOutput } from '@/ai/flows/generate-daily-tip';
import { useToast } from '@/hooks/use-toast';
import { db } from '@/lib/firebase';
import { collection, addDoc } from 'firebase/firestore';
import type { MoodEntry } from '@/types';
import { moodOptions, getNumericValue } from '@/app/(app)/mood-tracker/page'; 
import { Skeleton } from '@/components/ui/skeleton';

export default function DashboardPage() {
  const { user, userProfile, initialLoading, loading: authLoading } = useAuth();
  const router = useRouter();
  const { toast } = useToast();

  const [dailyTip, setDailyTip] = useState<GenerateDailyTipOutput | null>(null);
  const [isLoadingTip, setIsLoadingTip] = useState(true);
  
  const [selectedMood, setSelectedMood] = useState<string>('');
  const [moodDescription, setMoodDescription] = useState<string>('');
  const [isSubmittingMood, setIsSubmittingMood] = useState(false);

  useEffect(() => {
    // Auth checks are handled by AppLayout and page.tsx redirector
    if (user && userProfile) {
      const fetchTip = async () => {
        setIsLoadingTip(true);
        try {
          // In a production scenario, this tip would ideally be fetched from a pre-generated source
          // (e.g., Firestore document updated daily by a scheduled function)
          // to ensure all users see the same tip and to reduce API calls.
          const tip = await generateDailyTip({});
          setDailyTip(tip);
        } catch (error) {
          console.error("Error generando consejo del día:", error);
          toast({
            title: "Error",
            description: "No se pudo cargar el consejo del día.",
            variant: "destructive",
          });
          setDailyTip({ title: "Error al cargar", content: "Inténtalo de nuevo más tarde."});
        } finally {
          setIsLoadingTip(false);
        }
      };
      fetchTip();
    }
  }, [user, userProfile, toast]);

  const handleSaveMood = async () => {
    if (!user || !selectedMood) {
      toast({ title: "Información Faltante", description: "Por favor, selecciona un estado de ánimo.", variant: "destructive" });
      return;
    }
    setIsSubmittingMood(true);
    try {
      const newEntry: Omit<MoodEntry, 'id'> = {
        date: new Date().toISOString(), 
        mood: selectedMood,
        description: moodDescription,
        value: getNumericValue(selectedMood),
        createdAt: new Date(),
      };
      await addDoc(collection(db, `users/${user.uid}/moodEntries`), newEntry);
      toast({ title: "Ánimo Guardado", description: "Tu entrada de ánimo ha sido registrada." });
      setSelectedMood('');
      setMoodDescription('');
    } catch (error) {
      console.error("Error guardando ánimo:", error);
      toast({ title: "Error", description: "No se pudo guardar la entrada de ánimo.", variant: "destructive" });
    } finally {
      setIsSubmittingMood(false);
    }
  };

  // Initial loading for the page content itself, not auth which is handled by layout/redirector
  if (initialLoading || authLoading) {
     return (
      <div className="flex flex-col items-center justify-center min-h-[calc(100vh-200px)]">
        <Loader2 className="h-12 w-12 animate-spin text-primary" />
        <p className="mt-4 text-lg text-muted-foreground">Cargando tu espacio...</p>
      </div>
    );
  }


  return (
    <div className="space-y-8">
       <Card className="shadow-xl bg-card/80 backdrop-blur-sm border-border/50">
        <CardHeader>
          <CardTitle className="text-3xl font-headline flex items-center text-primary">
            <Sparkles className="mr-3 h-8 w-8" /> ¡Bienvenido/a de nuevo, {userProfile?.displayName || user?.displayName || 'Usuario'}!
          </CardTitle>
          <CardDescription className="text-lg text-muted-foreground">
            Aquí tienes un pequeño impulso para tu día y una forma rápida de conectar contigo.
          </CardDescription>
        </CardHeader>
      </Card>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
        <Card className="shadow-lg w-full">
          <CardHeader>
            <CardTitle className="text-2xl font-headline flex items-center text-accent">
              <Lightbulb className="mr-2 h-7 w-7" /> Consejo del Día
            </CardTitle>
          </CardHeader>
          <CardContent className="min-h-[100px]">
            {isLoadingTip ? (
              <div className="space-y-3 p-2">
                <Skeleton className="h-6 w-3/4" />
                <Skeleton className="h-4 w-full" />
                <Skeleton className="h-4 w-5/6" />
              </div>
            ) : dailyTip ? (
              <>
                <h3 className="text-xl font-semibold text-accent-foreground mb-2">{dailyTip.title}</h3>
                <p className="text-foreground/90 whitespace-pre-wrap">{dailyTip.content}</p>
              </>
            ) : (
              <p className="text-muted-foreground">No se pudo cargar el consejo del día.</p>
            )}
          </CardContent>
          <CardFooter>
            <p className="text-xs text-muted-foreground italic">
              Este consejo es generado por IA y es para inspiración general.
            </p>
          </CardFooter>
        </Card>

        <Card className="shadow-lg w-full">
          <CardHeader>
            <CardTitle className="text-2xl font-headline flex items-center text-secondary">
              <CalendarCheck className="mr-2 h-7 w-7" /> ¿Cómo te sientes hoy?
            </CardTitle>
            <CardDescription className="text-muted-foreground">Registra tu ánimo rápidamente. La fecha de hoy se usará automáticamente.</CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <label htmlFor="mood-select-home" className="text-sm font-medium block mb-1 text-foreground/80">Tu Ánimo</label>
              <Select value={selectedMood} onValueChange={setSelectedMood}>
                <SelectTrigger id="mood-select-home" className="w-full bg-input border-border/70 text-foreground placeholder:text-muted-foreground">
                  <SelectValue placeholder="Selecciona tu ánimo" />
                </SelectTrigger>
                <SelectContent>
                  {moodOptions.map(opt => (
                    <SelectItem key={opt.value} value={opt.value}>
                      <div className="flex items-center gap-2">
                        {React.cloneElement(opt.icon, { className: `${opt.icon.props.className} h-5 w-5`})} 
                        {opt.label}
                      </div>
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            <div>
              <label htmlFor="mood-description-home" className="text-sm font-medium block mb-1 text-foreground/80">Descripción (Opcional)</label>
              <Textarea 
                id="mood-description-home"
                value={moodDescription} 
                onChange={(e) => setMoodDescription(e.target.value)}
                placeholder="Algún pensamiento o evento relacionado..." 
                className="bg-input border-border/70 text-foreground placeholder:text-muted-foreground"
              />
            </div>
          </CardContent>
          <CardFooter>
            <Button onClick={handleSaveMood} disabled={isSubmittingMood || !selectedMood} className="w-full md:w-auto" variant="secondary">
              {isSubmittingMood ? <Loader2 className="mr-2 h-4 w-4 animate-spin" /> : null}
              {isSubmittingMood ? "Guardando..." : "Guardar Ánimo de Hoy"}
            </Button>
          </CardFooter>
        </Card>
      </div>
    </div>
  );
}
