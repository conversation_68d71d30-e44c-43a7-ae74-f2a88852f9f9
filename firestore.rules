rules_version = '2';

service cloud.firestore {
  match /databases/{database}/documents {
    // Allow users to manage their own profiles
    match /users/{userId} {
      allow read, update, delete: if request.auth != null && request.auth.uid == userId;
      allow create: if request.auth != null; // Anyone authenticated can create a user profile (usually for themselves)

      // Allow users to manage their own mood entries
      match /moodEntries/{moodEntryId} {
        allow read, write: if request.auth != null && request.auth.uid == userId;
      }
    }

    // Fallback rule: by default, deny all reads and writes to other collections
    match /{document=**} {
      allow read, write: if false;
    }
  }
}