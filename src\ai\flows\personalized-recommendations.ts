
'use server';
import { ai } from '@/ai/genkit';
import { z } from 'genkit';

// --- ZOD SCHEMAS ---
const PersonalizedRecommendationsInputSchema = z.object({
  uid: z.string().describe('El UID del usuario autenticado.'),
  moodEntries: z.string().describe('Entradas de ánimo del usuario.'),
  userPreferences: z.string().optional().describe('Preferencias del usuario.'),
});
export type PersonalizedRecommendationsInput = z.infer<typeof PersonalizedRecommendationsInputSchema>;

const PersonalizedRecommendationsOutputSchema = z.object({
  recommendation: z.string().describe('La recomendación generada.'),
  isPersonalized: z.boolean().describe('Indica si la recomendación fue personalizada.'),
});
export type PersonalizedRecommendationsOutput = z.infer<typeof PersonalizedRecommendationsOutputSchema>;

// --- FUNCIÓN PRINCIPAL DEL FLUJO ---
export async function personalizedRecommendations(input: PersonalizedRecommendationsInput): Promise<PersonalizedRecommendationsOutput> {
  // Lógica de pago y límites deshabilitada temporalmente.
  // Se asume que todos los usuarios son premium para que la app funcione.
  const isSubscribed = true; 
  let promptText = '';
  let isPersonalized = false;

  if (isSubscribed) {
    isPersonalized = true;
    promptText = `Basado en los siguientes datos de un usuario, genera una recomendación de bienestar **personalizada** y accionable. La respuesta DEBE ESTAR EN ESPAÑOL y ser concisa.

Entradas de Ánimo: ${input.moodEntries}
Preferencias: ${input.userPreferences || 'No especificadas'}`;
  } else {
    isPersonalized = false;
    promptText = `Genera un consejo de bienestar **general y útil** para cualquier persona. La respuesta DEBE ESTAR EN ESPAÑOL y ser concisa.`;
  }

  // Se usa un prompt dinámico para asegurar una estructura de respuesta correcta
  const dynamicPrompt = ai.definePrompt(
    {
      name: 'dynamicRecommendationPrompt',
      input: { schema: z.void() },
      output: { schema: z.object({ recommendation: z.string() }) },
      prompt: promptText,
    }
  );

  const { output } = await dynamicPrompt();

  if (!output) {
    throw new Error('No se pudo generar una recomendación desde la IA.');
  }

  return {
    recommendation: output.recommendation,
    isPersonalized: isPersonalized,
  };
}
