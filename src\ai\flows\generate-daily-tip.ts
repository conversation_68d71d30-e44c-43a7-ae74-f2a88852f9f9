
'use server';
/**
 * @fileOverview Flow para generar un consejo diario de bienestar.
 *
 * - generateDailyTip - Una función que genera el consejo.
 * - GenerateDailyTipInput - El tipo de entrada (vacío en este caso).
 * - GenerateDailyTipOutput - El tipo de retorno para el consejo.
 */

import {ai} from '@/ai/genkit';
import {z} from 'genkit';

const GenerateDailyTipInputSchema = z.object({}).describe("No se requiere entrada para este flujo.");
export type GenerateDailyTipInput = z.infer<typeof GenerateDailyTipInputSchema>;

const GenerateDailyTipOutputSchema = z.object({
  title: z.string().describe('El título conciso del consejo del día.'),
  content: z.string().describe('El contenido del consejo del día, breve y accionable (1-3 frases).'),
});
export type GenerateDailyTipOutput = z.infer<typeof GenerateDailyTipOutputSchema>;

export async function generateDailyTip(input: GenerateDailyTipInput): Promise<GenerateDailyTipOutput> {
  return generateDailyTipFlow(input);
}

const prompt = ai.definePrompt({
  name: 'generateDailyTipPrompt',
  input: {schema: GenerateDailyTipInputSchema},
  output: {schema: GenerateDailyTipOutputSchema},
  prompt: `Eres un asistente de IA especializado en bienestar mental y positividad.
      Tu tarea principal es generar un "Consejo del Día".
      **IMPORTANTE: El consejo DEBE ESTAR ÍNTEGRAMENTE EN ESPAÑOL.** No utilices inglés bajo ninguna circunstancia para el título o el contenido del consejo.
      El consejo debe ser breve, positivo, práctico y aplicable para un público general.
      Debe tener un título corto y llamativo (también en español), y el contenido del consejo no debe exceder 2-3 frases concisas (en español).
      Enfócate en temas como mindfulness, manejo del estrés, gratitud, pequeñas acciones para mejorar el ánimo, o pensamientos positivos.
      Evita consejos médicos complejos o que requieran un contexto muy específico. Hazlo universal y fácil de entender. Asegúrate de que TODO el resultado final esté en perfecto ESPAÑOL.

Ejemplos de formato deseado:
Título: "Pequeña Pausa, Gran Impacto"
Contenido: "Dedica solo 5 minutos hoy a respirar profundamente. Observa cómo tu cuerpo se relaja y tu mente se aclara. Esta simple acción puede recargar tu energía."

Título: "Celebra lo Cotidiano"
Contenido: "Encuentra un pequeño momento o detalle de tu día por el cual sentirte agradecido. Reconocer lo bueno, por pequeño que sea, cultiva la alegría."

Genera un nuevo consejo del día ahora.`,
});

const generateDailyTipFlow = ai.defineFlow(
  {
    name: 'generateDailyTipFlow',
    inputSchema: GenerateDailyTipInputSchema,
    outputSchema: GenerateDailyTipOutputSchema,
  },
  async (input) => {
    const {output} = await prompt(input);
    return output!;
  }
);

    