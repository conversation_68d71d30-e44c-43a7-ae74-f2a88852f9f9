import { genkit } from 'genkit';
import type { Genkit as GenkitInstanceType } from 'genkit';
import { googleAI } from '@genkit-ai/googleai';

let ai: GenkitInstanceType;

if (process.env.NODE_ENV === 'production') {
  ai = genkit({
    plugins: [googleAI({ apiKey: process.env.GOOGLE_API_KEY })],
    model: 'googleai/gemini-2.0-flash',
  });
} else {
  if (!globalThis.__genkit_ai_instance) {
    globalThis.__genkit_ai_instance = genkit({
      plugins: [googleAI({ apiKey: process.env.GOOGLE_API_KEY })],
      model: 'googleai/gemini-2.0-flash',
    });
  }
  ai = globalThis.__genkit_ai_instance;
}

export { ai };
