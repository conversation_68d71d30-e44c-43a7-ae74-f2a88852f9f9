
'use client';

import React from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Check, X } from 'lucide-react';
import { useRouter } from 'next/navigation';
import { cn } from '@/lib/utils';

const features = [
  { name: 'Seguimiento de Ánimo', free: 'Completo', monthly: 'Completo', annual: 'Completo' },
  { name: 'Chat con IA', free: '5 mensajes/día', monthly: 'Ilimitado', annual: 'Ilimitado' },
  { name: 'Recomendaciones de IA', free: 'Genéricas', monthly: 'Personalizadas', annual: 'Personalizadas' },
  { name: 'Acceso a Artículos', free: '3 al mes', monthly: 'Ilimitado', annual: 'Ilimitado' },
  { name: 'Red de Doctores', free: true, monthly: true, annual: true },
  { name: 'Soporte al Cliente', free: '<PERSON>st<PERSON><PERSON>', monthly: 'Prioritario', annual: 'Prioritario' },
];

const PlanCard = ({ plan, className }: { plan: 'free' | 'monthly' | 'annual', className?: string }) => {
  const router = useRouter();

  const handleSubscription = (p: 'monthly' | 'annual') => {
    // TODO: Implementar la lógica de compra con RevenueCat
    console.log(`Iniciando suscripción para el plan: ${p}`);
  };

  const planDetails = {
    free: {
      title: 'Gratis',
      price: '$0',
      description: 'Para empezar a explorar',
      buttonText: 'Tu Plan Actual',
      buttonAction: () => {},
      buttonVariant: 'outline',
      disabled: true,
      features: features.map(f => ({ name: f.name, value: f.free })),
    },
    monthly: {
      title: 'Mensual',
      price: '$199',
      description: 'MXN / mes',
      buttonText: 'Seleccionar Plan',
      buttonAction: () => handleSubscription('monthly'),
      buttonVariant: 'default',
      disabled: false,
      features: features.map(f => ({ name: f.name, value: f.monthly })),
    },
    annual: {
      title: 'Anual',
      price: '$999',
      description: 'MXN / año',
      buttonText: 'Seleccionar Plan',
      buttonAction: () => handleSubscription('annual'),
      buttonVariant: 'default',
      disabled: false,
      features: features.map(f => ({ name: f.name, value: f.annual })),
      badge: 'Más Popular',
    },
  };

  const currentPlan = planDetails[plan];

  const renderFeature = (value: boolean | string) => {
    if (typeof value === 'boolean' && value) {
      return <Check className="h-5 w-5 text-green-500" />;
    }
    if (typeof value === 'boolean' && !value) {
      return <X className="h-5 w-5 text-muted-foreground" />;
    }
    return <span className="text-sm font-medium">{value}</span>;
  };

  return (
    <Card className={cn("flex flex-col", className)}>
        <CardHeader className="flex-grow-0">
            <div className="flex justify-between items-center">
                <CardTitle className="text-2xl">{currentPlan.title}</CardTitle>
                {currentPlan.badge && <span className="bg-primary text-primary-foreground text-xs font-bold uppercase px-3 py-1 rounded-full">{currentPlan.badge}</span>}
            </div>
            <div className="flex items-baseline gap-x-2">
                <span className="text-4xl font-bold tracking-tight">{currentPlan.price}</span>
                {currentPlan.description && <span className="text-sm font-semibold leading-6 tracking-wide text-muted-foreground">{currentPlan.description}</span>}
            </div>
        </CardHeader>
        <CardContent className="flex-1">
            <ul className="space-y-4">
                {currentPlan.features.map((feature) => (
                    <li key={feature.name} className="flex justify-between">
                        <span className="text-sm text-muted-foreground">{feature.name}</span>
                        {renderFeature(feature.value)}
                    </li>
                ))}
            </ul>
        </CardContent>
        <CardFooter>
            <Button 
                onClick={currentPlan.buttonAction} 
                className="w-full text-lg py-6"
                variant={currentPlan.buttonVariant as any}
                disabled={currentPlan.disabled}
            >
                {currentPlan.buttonText}
            </Button>
        </CardFooter>
    </Card>
  );
};


export default function SubscribePage() {
  const router = useRouter();

  return (
    <div className="flex flex-col items-center justify-center p-4 md:p-8">
      <div className="mx-auto max-w-4xl text-center">
        <h1 className="text-4xl font-headline text-primary">Elige Tu Plan</h1>
        <p className="mt-4 text-lg text-muted-foreground">
          Comienza gratis o desbloquea todo tu potencial con un plan Premium.
        </p>
      </div>

      <div className="isolate mx-auto mt-10 grid max-w-md grid-cols-1 gap-8 lg:mx-0 lg:max-w-none lg:grid-cols-3">
        <PlanCard plan="free" />
        <PlanCard plan="monthly" />
        <PlanCard plan="annual" className="border-2 border-primary ring-2 ring-primary ring-offset-2 ring-offset-background shadow-primary/20" />
      </div>

       <Button variant="link" onClick={() => router.back()} className="mt-8">
          Quizás más tarde
       </Button>
    </div>
  );
}
