
export interface UserProfile {
    uid: string;
    email?: string;
    displayName?: string;
    age?: number;
    gender?: string;
    therapistGender?: 'male' | 'female'; // <-- Nueva propiedad
    mentalHealthHistory?: string;
    preferences?: string;
    personalizedGreeting?: string;
    initialRecommendations?: string[] | string;
    createdAt: Date;
    updatedAt?: Date;
    subscription?: {
        status: string;
        type: string;
        expires_date: string;
        provider: string;
    };
    usage?: {
        chatMessages?: {
            count: number;
            lastMessageDate: string;
        };
        articlesViewed?: {
            count: number;
            lastViewedMonth: string;
        };
    }
}

export interface ChatMessage {
  id: string;
  text: string;
  sender: 'user' | 'ai';
  timestamp: Date;
}

export type Article = GenerateArticleOutput & {
  id: string;
  savedAt?: unknown;
};

export type GenerateArticleOutput = {
  title: string;
  content: string;
  imageUrl?: string;
  topic?: string;
};

export interface MoodEntry {
  id: string;
  mood: string;
  intensity: number;
  description?: string;
  date: string;
  createdAt: Date;
}
