
'use client';

import React, { useState, useEffect } from 'react';
import { useAuth } from '@/contexts/auth-context';
import type { MoodEntry } from '@/types';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Textarea } from '@/components/ui/textarea';
import { Calendar } from '@/components/ui/calendar';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { BarChart3, CalendarIcon, Smile, Frown, Meh, PlusCircle, AlertCircle } from 'lucide-react';
import { format } from 'date-fns';
import { es } from 'date-fns/locale'; 
import { LineChart, Line, XAxis, YAxis, CartesianGrid, <PERSON>lt<PERSON>, Responsive<PERSON><PERSON><PERSON>, <PERSON> } from 'recharts';
import { db } from '@/lib/firebase';
import { collection, addDoc, query, orderBy, onSnapshot, Timestamp } from 'firebase/firestore';
import { useToast } from '@/hooks/use-toast';
import { Skeleton } from '@/components/ui/skeleton';

export const moodOptions = [
  { label: 'Extático/a', value: 'ecstatic', numeric: 5, icon: <Smile className="text-green-500" /> },
  { label: 'Feliz', value: 'happy', numeric: 4, icon: <Smile className="text-lime-500" /> },
  { label: 'Neutral', value: 'neutral', numeric: 3, icon: <Meh className="text-yellow-500" /> },
  { label: 'Triste', value: 'sad', numeric: 2, icon: <Frown className="text-blue-500" /> },
  { label: 'Ansioso/a', value: 'anxious', numeric: 2, icon: <Frown className="text-purple-500" /> },
  { label: 'Estresado/a', value: 'stressed', numeric: 1, icon: <Frown className="text-orange-500" /> },
  { label: 'Deprimido/a', value: 'depressed', numeric: 1, icon: <Frown className="text-indigo-500" /> },
];

export const getNumericValue = (moodValue: string) => {
  return moodOptions.find(m => m.value === moodValue)?.numeric || 0;
};

export default function MoodTrackerPage() {
  const { user } = useAuth();
  const { toast } = useToast();
  const [date, setDate] = useState<Date | undefined>(new Date());
  const [selectedMood, setSelectedMood] = useState<string>('');
  const [description, setDescription] = useState<string>('');
  const [moodHistory, setMoodHistory] = useState<MoodEntry[]>([]);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isLoadingHistory, setIsLoadingHistory] = useState(true);

  useEffect(() => {
    if (!user) return;
    setIsLoadingHistory(true);
    const q = query(
      collection(db, `users/${user.uid}/moodEntries`), 
      orderBy('createdAt', 'desc')
    );

    const unsubscribe = onSnapshot(q, (querySnapshot) => {
      const history: MoodEntry[] = [];
      querySnapshot.forEach((doc) => {
        const data = doc.data();
        history.push({ 
          id: doc.id, 
          ...data,
          date: (data.date as Timestamp)?.toDate ? (data.date as Timestamp).toDate().toISOString() : data.date as string,
          createdAt: (data.createdAt as Timestamp).toDate() 
        } as MoodEntry);
      });
      setMoodHistory(history.sort((a,b) => new Date(b.date).getTime() - new Date(a.date).getTime()));
      setIsLoadingHistory(false);
    }, (error) => {
      console.error("Error obteniendo historial de ánimo:", error);
      toast({ title: "Error", description: "No se pudo obtener el historial de ánimo.", variant: "destructive" });
      setIsLoadingHistory(false);
    });

    return () => unsubscribe();
  }, [user, toast]);

  const handleSaveMood = async () => {
    if (!user || !date || !selectedMood) {
      toast({ title: "Información Faltante", description: "Por favor, selecciona una fecha y un estado de ánimo.", variant: "destructive" });
      return;
    }
    setIsSubmitting(true);
    try {
      const newEntry: Omit<MoodEntry, 'id'> = {
        date: date.toISOString(),
        mood: selectedMood,
        description: description,
        value: getNumericValue(selectedMood),
        createdAt: new Date(),
      };
      await addDoc(collection(db, `users/${user.uid}/moodEntries`), newEntry);
      toast({ title: "Ánimo Guardado", description: "Tu entrada de ánimo ha sido registrada." });
      setSelectedMood('');
      setDescription('');
      setDate(new Date());
    } catch (error) {
      console.error("Error guardando ánimo:", error);
      toast({ title: "Error", description: "No se pudo guardar la entrada de ánimo.", variant: "destructive" });
    } finally {
      setIsSubmitting(false);
    }
  };

  const chartData = moodHistory
    .map(entry => ({
      date: format(new Date(entry.date), 'MMM d', { locale: es }),
      mood: entry.mood,
      value: entry.value,
    }))
    .sort((a,b) => new Date(a.date).getTime() - new Date(b.date).getTime()) 
    .slice(0, 30); 

  return (
    <div className="space-y-8">
      <Card className="shadow-lg">
        <CardHeader>
          <CardTitle className="text-2xl font-headline flex items-center">
            <PlusCircle className="mr-2 h-7 w-7 text-primary" /> Registra Tu Ánimo
          </CardTitle>
          <CardDescription>¿Cómo te sientes hoy? Registrar tu ánimo puede ayudarte a entender patrones y desencadenantes.</CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="space-y-2">
              <label className="text-sm font-medium">Fecha</label>
              <Popover>
                <PopoverTrigger asChild>
                  <Button
                    variant={"outline"}
                    className="w-full justify-start text-left font-normal"
                  >
                    <CalendarIcon className="mr-2 h-4 w-4" />
                    {date ? format(date, "PPP", { locale: es }) : <span>Elige una fecha</span>}
                  </Button>
                </PopoverTrigger>
                <PopoverContent className="w-auto p-0">
                  <Calendar
                    mode="single"
                    selected={date}
                    onSelect={setDate}
                    initialFocus
                    locale={es} 
                  />
                </PopoverContent>
              </Popover>
            </div>
            <div className="space-y-2">
              <label className="text-sm font-medium">Ánimo</label>
              <Select value={selectedMood} onValueChange={setSelectedMood}>
                <SelectTrigger className="w-full">
                  <SelectValue placeholder="Selecciona tu ánimo" />
                </SelectTrigger>
                <SelectContent>
                  {moodOptions.map(opt => (
                    <SelectItem key={opt.value} value={opt.value}>
                      <div className="flex items-center gap-2">
                        {opt.icon} {opt.label}
                      </div>
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>
          <div className="space-y-2">
            <label className="text-sm font-medium">Descripción (Opcional)</label>
            <Textarea 
              value={description} 
              onChange={(e) => setDescription(e.target.value)}
              placeholder="¿Qué tienes en mente? ¿Algún pensamiento o evento específico?" 
            />
          </div>
        </CardContent>
        <CardFooter>
          <Button onClick={handleSaveMood} disabled={isSubmitting || !selectedMood || !date} className="w-full md:w-auto">
            {isSubmitting ? "Guardando..." : "Guardar Ánimo"}
          </Button>
        </CardFooter>
      </Card>

      <Card className="shadow-lg">
        <CardHeader>
          <CardTitle className="text-2xl font-headline flex items-center">
            <BarChart3 className="mr-2 h-7 w-7 text-primary" /> Historial y Tendencias de Ánimo
          </CardTitle>
          <CardDescription>Visualiza tus patrones de ánimo a lo largo del tiempo.</CardDescription>
        </CardHeader>
        <CardContent>
          {isLoadingHistory ? (
            <div className="space-y-4">
              <Skeleton className="h-64 w-full" />
              <Skeleton className="h-10 w-full" />
              <Skeleton className="h-10 w-full" />
            </div>
          ) : moodHistory.length > 0 ? (
            <div className="space-y-6">
              <div className="h-[300px] w-full">
                <ResponsiveContainer width="100%" height="100%">
                  <LineChart data={chartData}>
                    <CartesianGrid strokeDasharray="3 3" stroke="hsl(var(--border))" />
                    <XAxis dataKey="date" stroke="hsl(var(--muted-foreground))" />
                    <YAxis domain={[0, 5]} tickCount={6} stroke="hsl(var(--muted-foreground))" />
                    <Tooltip
                      contentStyle={{ backgroundColor: 'hsl(var(--popover))', borderColor: 'hsl(var(--border))' }}
                      labelStyle={{ color: 'hsl(var(--popover-foreground))' }}
                       formatter={(value: number, name: string, props) => [value, 'Nivel de Ánimo']}
                    />
                    <Legend wrapperStyle={{ color: 'hsl(var(--foreground))' }} formatter={(value) => "Nivel de Ánimo"} />
                    <Line type="monotone" dataKey="value" name="Nivel de Ánimo" stroke="hsl(var(--primary))" strokeWidth={2} activeDot={{ r: 6 }} />
                  </LineChart>
                </ResponsiveContainer>
              </div>
              <h3 className="text-lg font-semibold mt-6 mb-2">Entradas Recientes:</h3>
              <ul className="space-y-2 max-h-60 overflow-y-auto">
                {moodHistory.slice(0,10).map(entry => ( 
                  <li key={entry.id} className="p-3 border rounded-md bg-muted/30">
                    <div className="flex justify-between items-center">
                      <span className="font-medium flex items-center">
                        {moodOptions.find(m => m.value === entry.mood)?.icon}
                        <span className="ml-2">{moodOptions.find(m => m.value === entry.mood)?.label || entry.mood}</span>
                      </span>
                      <span className="text-sm text-muted-foreground">{format(new Date(entry.date), 'MMM d, yyyy', { locale: es })}</span>
                    </div>
                    {entry.description && <p className="text-sm text-muted-foreground mt-1">{entry.description}</p>}
                  </li>
                ))}
              </ul>
            </div>
          ) : (
            <div className="text-center py-8">
              <AlertCircle className="mx-auto h-12 w-12 text-muted-foreground mb-2" />
              <p className="text-muted-foreground">Aún no hay entradas de ánimo. Empieza a registrar tu ánimo para ver tu historial aquí.</p>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}

    