
import * as functions from "firebase-functions";
import * as admin from "firebase-admin";

admin.initializeApp();

/**
 * Webhook para recibir notificaciones de RevenueCat.
 *
 * Esta función se activa cuando RevenueCat envía un evento (ej. una nueva suscripción).
 * URL para configurar en RevenueCat:
 * https://<REGION>-<PROJECT_ID>.cloudfunctions.net/revenueCatWebhook
 */
export const revenueCatWebhook = functions.https.onRequest((request, response) => {
  functions.logger.info("¡Webhook de RevenueCat recibido!", {
    headers: request.headers,
    body: request.body,
  });

  // TODO: Añadir lógica para verificar y procesar el evento de RevenueCat.
  
  response.status(200).send("Webhook recibido con éxito.");
});
