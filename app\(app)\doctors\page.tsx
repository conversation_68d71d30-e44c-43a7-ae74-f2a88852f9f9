'use client';

import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle, CardFooter } from '@/components/ui/card';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Users, Mail, Phone, Stethoscope } from 'lucide-react';
import { But<PERSON> } from '@/components/ui/button';
import Image from 'next/image';

const doctors = [
  {
    id: '1',
    name: '<PERSON><PERSON><PERSON> <PERSON>',
    specialty: 'Psicóloga Clínica, Especialista en TCC',
    imageUrl: 'https://placehold.co/128x128.png',
    dataAiHint: 'woman psychologist professional',
    bio: 'La Dra. Rodríguez tiene más de 10 años de experiencia ayudando a individuos a superar la ansiedad y la depresión usando Terapia Cognitivo-Conductual.',
    email: '<EMAIL>',
    phone: '(*************'
  },
  {
    id: '2',
    name: 'Dr. <PERSON>',
    specialty: '<PERSON><PERSON><PERSON><PERSON><PERSON>, Experto en Mindfulness',
    imageUrl: 'https://placehold.co/128x128.png',
    dataAiHint: 'man psychiatrist kind',
    bio: 'El <PERSON>. <PERSON> se especializa en el manejo de medicación e integrando prácticas de mindfulness para el bienestar mental holístico.',
    email: '<EMAIL>',
    phone: '(*************'
  },
  {
    id: '3',
    name: 'Dra. Aisha Khan',
    specialty: 'Terapeuta Licenciada, Cuidado Informado en Trauma',
    imageUrl: 'https://placehold.co/128x128.png',
    dataAiHint: 'woman therapist empathetic',
    bio: 'La Dra. Khan provee un espacio compasivo para la sanación, enfocándose en la recuperación de trauma y la construcción de resiliencia.',
    email: '<EMAIL>',
    phone: '(*************'
  },
   {
    id: '4',
    name: 'Dr. Samuel Green',
    specialty: 'Psicólogo Consejero, Psicología Positiva',
    imageUrl: 'https://placehold.co/128x128.png',
    dataAiHint: 'man psychologist friendly',
    bio: 'El Dr. Green se enfoca en enfoques basados en fortalezas e intervenciones de psicología positiva para ayudar a los clientes a florecer.',
    email: '<EMAIL>',
    phone: '(*************'
  },
];

export default function DoctorsNetworkPage() {
  return (
    <div className="space-y-8">
      <Card className="shadow-lg">
        <CardHeader>
          <CardTitle className="text-2xl font-headline flex items-center">
            <Users className="mr-2 h-7 w-7 text-primary" /> Nuestra Red de Profesionales
          </CardTitle>
          <CardDescription>
            Mente en calma colabora con profesionales de la salud mental experimentados y compasivos.
            Aunque nuestra IA ofrece apoyo, estos expertos pueden proporcionar cuidado en profundidad.
          </CardDescription>
        </CardHeader>
        <CardContent>
          <p className="mb-6 text-sm text-muted-foreground">
            Por favor, ten en cuenta: Contactar a estos profesionales es para fines informativos o para preguntar sobre sus servicios directamente. Mente en calma no facilita reservas directas a través de la aplicación en este momento.
          </p>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {doctors.map((doctor) => (
              <Card key={doctor.id} className="flex flex-col overflow-hidden shadow-md hover:shadow-xl transition-shadow duration-300">
                <CardHeader className="flex flex-row items-center gap-4 p-4 bg-muted/30">
                  <Avatar className="h-20 w-20 border-2 border-primary">
                    <Image src={doctor.imageUrl} alt={doctor.name} width={80} height={80} className="object-cover" data-ai-hint={doctor.dataAiHint}/>
                    <AvatarFallback>{doctor.name.substring(0, 2).toUpperCase()}</AvatarFallback>
                  </Avatar>
                  <div>
                    <CardTitle className="text-xl font-headline">{doctor.name}</CardTitle>
                    <CardDescription className="text-sm text-primary flex items-center">
                      <Stethoscope className="h-4 w-4 mr-1" /> {doctor.specialty}
                    </CardDescription>
                  </div>
                </CardHeader>
                <CardContent className="p-4 flex-grow">
                  <p className="text-sm text-muted-foreground mb-3">{doctor.bio}</p>
                  <div className="space-y-1 text-sm">
                    <a href={`mailto:${doctor.email}`} className="flex items-center text-accent hover:underline">
                      <Mail className="h-4 w-4 mr-2"/> {doctor.email}
                    </a>
                     <a href={`tel:${doctor.phone}`} className="flex items-center text-accent hover:underline">
                      <Phone className="h-4 w-4 mr-2"/> {doctor.phone}
                    </a>
                  </div>
                </CardContent>
                <CardFooter className="p-4 bg-muted/20">
                  <Button variant="outline" className="w-full">
                    Más Información (Próximamente)
                  </Button>
                </CardFooter>
              </Card>
            ))}
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
