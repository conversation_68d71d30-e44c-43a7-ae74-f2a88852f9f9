[debug] [2025-06-17T06:21:41.260Z] [Firebase Plugin] Activating Firebase extension.
[debug] [2025-06-17T06:21:43.097Z] [Firebase Plugin] requireAuthWrapper
[debug] [2025-06-17T06:21:43.105Z] > command requires scopes: ["email","openid","https://www.googleapis.com/auth/cloudplatformprojects.readonly","https://www.googleapis.com/auth/firebase","https://www.googleapis.com/auth/cloud-platform"]
[debug] [2025-06-17T06:21:43.106Z] > authorizing via signed-in user (<EMAIL>)
[debug] [2025-06-17T06:21:43.113Z] Checked if tokens are valid: false, expires at: 1750142043503
[debug] [2025-06-17T06:21:43.114Z] Checked if tokens are valid: false, expires at: 1750142043503
[debug] [2025-06-17T06:21:43.115Z] > refreshing access token with scopes: []
[debug] [2025-06-17T06:21:43.122Z] >>> [apiv2][query] POST https://www.googleapis.com/oauth2/v3/token [none]
[debug] [2025-06-17T06:21:43.123Z] >>> [apiv2][body] POST https://www.googleapis.com/oauth2/v3/token [omitted]
[debug] [2025-06-17T06:21:43.155Z] [Firebase Plugin] VSCode notification server listening on port 40001
[debug] [2025-06-17T06:21:44.226Z] [Firebase Plugin] Value of process.env.MONOSPACE_ENV: undefined
[debug] [2025-06-17T06:21:44.228Z] [Firebase Plugin] requireAuthWrapper
[debug] [2025-06-17T06:21:44.232Z] > command requires scopes: ["email","openid","https://www.googleapis.com/auth/cloudplatformprojects.readonly","https://www.googleapis.com/auth/firebase","https://www.googleapis.com/auth/cloud-platform"]
[debug] [2025-06-17T06:21:44.238Z] > authorizing via signed-in user (<EMAIL>)
[debug] [2025-06-17T06:21:44.240Z] Checked if tokens are valid: false, expires at: 1750142043503
[debug] [2025-06-17T06:21:44.240Z] Checked if tokens are valid: false, expires at: 1750142043503
[debug] [2025-06-17T06:21:44.241Z] > refreshing access token with scopes: []
[debug] [2025-06-17T06:21:44.245Z] >>> [apiv2][query] POST https://www.googleapis.com/oauth2/v3/token [none]
[debug] [2025-06-17T06:21:44.246Z] >>> [apiv2][body] POST https://www.googleapis.com/oauth2/v3/token [omitted]
[debug] [2025-06-17T06:21:44.267Z] [Firebase Plugin] Value of process.env.MONOSPACE_ENV: undefined
[debug] [2025-06-17T06:21:44.272Z] [Firebase Plugin] requireAuthWrapper
[debug] [2025-06-17T06:21:44.277Z] > command requires scopes: ["email","openid","https://www.googleapis.com/auth/cloudplatformprojects.readonly","https://www.googleapis.com/auth/firebase","https://www.googleapis.com/auth/cloud-platform"]
[debug] [2025-06-17T06:21:44.278Z] > authorizing via signed-in user (<EMAIL>)
[debug] [2025-06-17T06:21:44.280Z] Checked if tokens are valid: false, expires at: 1750142043503
[debug] [2025-06-17T06:21:44.281Z] Checked if tokens are valid: false, expires at: 1750142043503
[debug] [2025-06-17T06:21:44.282Z] > refreshing access token with scopes: []
[debug] [2025-06-17T06:21:44.284Z] >>> [apiv2][query] POST https://www.googleapis.com/oauth2/v3/token [none]
[debug] [2025-06-17T06:21:44.287Z] >>> [apiv2][body] POST https://www.googleapis.com/oauth2/v3/token [omitted]
[debug] [2025-06-17T06:21:44.349Z] <<< [apiv2][status] POST https://www.googleapis.com/oauth2/v3/token 200
[debug] [2025-06-17T06:21:44.349Z] <<< [apiv2][body] POST https://www.googleapis.com/oauth2/v3/token [omitted]
[debug] [2025-06-17T06:21:44.604Z] [Firebase Plugin] User found:  <EMAIL>
[info] [Firebase Plugin] (Core:Project) New user detected, fetching projects
[debug] [2025-06-17T06:21:44.609Z] Checked if tokens are valid: true, expires at: 1750144903350
[debug] [2025-06-17T06:21:44.614Z] >>> [apiv2][query] GET https://firebase.googleapis.com/v1beta1/projects pageSize=1000
[debug] [2025-06-17T06:21:44.806Z] <<< [apiv2][status] POST https://www.googleapis.com/oauth2/v3/token 200
[debug] [2025-06-17T06:21:44.807Z] <<< [apiv2][body] POST https://www.googleapis.com/oauth2/v3/token [omitted]
[debug] [2025-06-17T06:21:45.133Z] [Firebase Plugin] User found:  <EMAIL>
[info] [Firebase Plugin] (Core:Project) New user detected, fetching projects
[debug] [2025-06-17T06:21:45.144Z] Checked if tokens are valid: true, expires at: 1750144903807
[debug] [2025-06-17T06:21:45.146Z] >>> [apiv2][query] GET https://firebase.googleapis.com/v1beta1/projects pageSize=1000
[debug] [2025-06-17T06:21:45.240Z] <<< [apiv2][status] POST https://www.googleapis.com/oauth2/v3/token 200
[debug] [2025-06-17T06:21:45.241Z] <<< [apiv2][body] POST https://www.googleapis.com/oauth2/v3/token [omitted]
[debug] [2025-06-17T06:21:45.343Z] [Firebase Plugin] User found:  <EMAIL>
[info] [Firebase Plugin] (Core:Project) New user detected, fetching projects
[debug] [2025-06-17T06:21:45.346Z] Checked if tokens are valid: true, expires at: 1750144904241
[debug] [2025-06-17T06:21:45.348Z] >>> [apiv2][query] GET https://firebase.googleapis.com/v1beta1/projects pageSize=1000
[debug] [2025-06-17T06:21:45.716Z] <<< [apiv2][status] GET https://firebase.googleapis.com/v1beta1/projects 200
[debug] [2025-06-17T06:21:45.716Z] <<< [apiv2][body] GET https://firebase.googleapis.com/v1beta1/projects [omitted]
[debug] [2025-06-17T06:21:45.880Z] <<< [apiv2][status] GET https://firebase.googleapis.com/v1beta1/projects 200
[debug] [2025-06-17T06:21:45.881Z] <<< [apiv2][body] GET https://firebase.googleapis.com/v1beta1/projects [omitted]
[debug] [2025-06-17T06:21:45.958Z] <<< [apiv2][status] GET https://firebase.googleapis.com/v1beta1/projects 200
[debug] [2025-06-17T06:21:45.959Z] <<< [apiv2][body] GET https://firebase.googleapis.com/v1beta1/projects [omitted]
