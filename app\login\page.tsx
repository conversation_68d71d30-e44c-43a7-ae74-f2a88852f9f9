
'use client';

import React, { useEffect, useState } from 'react';
import { useRouter } from 'next/navigation';
import { useAuth } from '@/contexts/auth-context';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Brain, LogIn, Mail, KeyRound, UserPlus } from 'lucide-react';
import { useToast } from '@/hooks/use-toast';
import { useForm, type SubmitHandler } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";

const signInSchema = z.object({
  email: z.string().email("Correo electrónico inválido."),
  password: z.string().min(6, "La contraseña debe tener al menos 6 caracteres."),
});
type SignInFormData = z.infer<typeof signInSchema>;

const signUpSchema = z.object({
  displayName: z.string().min(2, "El nombre debe tener al menos 2 caracteres.").optional(),
  email: z.string().email("Correo electrónico inválido."),
  password: z.string().min(6, "La contraseña debe tener al menos 6 caracteres."),
  confirmPassword: z.string().min(6, "La contraseña debe tener al menos 6 caracteres."),
}).refine(data => data.password === data.confirmPassword, {
  message: "Las contraseñas no coinciden.",
  path: ["confirmPassword"],
});
type SignUpFormData = z.infer<typeof signUpSchema>;

export default function LoginPage() {
  const { 
    user,
    signInWithGoogle, 
    signUpWithEmail, 
    signInWithEmail, 
    loading,
    initialLoading,
    authError 
  } = useAuth();
  const router = useRouter();
  const { toast } = useToast();
  const [activeTab, setActiveTab] = useState("signin");

  const signInForm = useForm<SignInFormData>({ resolver: zodResolver(signInSchema), defaultValues: { email: '', password: '' } });
  const signUpForm = useForm<SignUpFormData>({ resolver: zodResolver(signUpSchema), defaultValues: { displayName: '', email: '', password: '', confirmPassword: '' } });

  useEffect(() => {
    if (authError) {
      toast({ title: "Error de Autenticación", description: authError, variant: "destructive" });
    }
  }, [authError, toast]);

  useEffect(() => {
    if (!initialLoading && user) {
      router.replace('/dashboard');
    }
  }, [user, initialLoading, router]);

  const handleGoogleSignIn = async () => { await signInWithGoogle(); };
  const onSignInSubmit: SubmitHandler<SignInFormData> = async (data) => { await signInWithEmail(data.email, data.password); };
  const onSignUpSubmit: SubmitHandler<SignUpFormData> = async (data) => { await signUpWithEmail(data.email, data.password, data.displayName); };
  
  if (initialLoading) { 
    return (
      <div className="flex h-screen w-screen items-center justify-center bg-background">
        <Brain className="h-16 w-16 animate-pulse text-primary" />
        <p className="ml-4 text-lg">Verificando...</p>
      </div>
    );
  }
  
  // Si ya hay un usuario, no mostrar nada mientras se redirige.
  if (user) {
      return (
          <div className="flex h-screen w-screen items-center justify-center bg-background">
              <Brain className="h-16 w-16 animate-pulse text-primary" />
              <p className="ml-4 text-lg">Redirigiendo...</p>
          </div>
      );
  }

  return (
    <div className="flex min-h-screen flex-col items-center justify-center bg-gradient-to-br from-background to-secondary p-4">
      <Card className="w-full max-w-md shadow-2xl">
        <CardHeader className="text-center">
          <div className="mx-auto mb-4 flex h-16 w-16 items-center justify-center rounded-full bg-primary/10 text-primary"><Brain className="h-10 w-10" /></div>
          <CardTitle className="text-3xl font-headline">Bienvenido/a a Mente en calma</CardTitle>
          <CardDescription className="text-md">Tu compañero personal de IA para el bienestar mental.</CardDescription>
        </CardHeader>
        <CardContent>
          <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
            <TabsList className="grid w-full grid-cols-2">
              <TabsTrigger value="signin">Iniciar Sesión</TabsTrigger>
              <TabsTrigger value="signup">Crear Cuenta</TabsTrigger>
            </TabsList>
            <TabsContent value="signin">
              <Form {...signInForm}><form onSubmit={signInForm.handleSubmit(onSignInSubmit)} className="space-y-6 mt-6">
                <FormField control={signInForm.control} name="email" render={({ field }) => (
                  <FormItem><FormLabel>Correo</FormLabel><FormControl><div className="relative"><Mail className="absolute left-3 top-1/2 h-5 w-5 -translate-y-1/2 text-muted-foreground" /><Input type="email" placeholder="<EMAIL>" {...field} className="pl-10" /></div></FormControl><FormMessage /></FormItem>
                )} />
                <FormField control={signInForm.control} name="password" render={({ field }) => (
                  <FormItem><FormLabel>Contraseña</FormLabel><FormControl><div className="relative"><KeyRound className="absolute left-3 top-1/2 h-5 w-5 -translate-y-1/2 text-muted-foreground" /><Input type="password" placeholder="••••••••" {...field} className="pl-10" /></div></FormControl><FormMessage /></FormItem>
                )} />
                <Button type="submit" disabled={loading} className="w-full"><LogIn className="mr-2 h-5 w-5" /> Iniciar Sesión</Button>
              </form></Form>
            </TabsContent>
            <TabsContent value="signup">
              <Form {...signUpForm}><form onSubmit={signUpForm.handleSubmit(onSignUpSubmit)} className="space-y-6 mt-6">
                 <FormField control={signUpForm.control} name="displayName" render={({ field }) => (
                  <FormItem><FormLabel>Nombre</FormLabel><FormControl><div className="relative"><UserPlus className="absolute left-3 top-1/2 h-5 w-5 -translate-y-1/2 text-muted-foreground" /><Input placeholder="Tu nombre" {...field} className="pl-10" /></div></FormControl><FormMessage /></FormItem>
                )} />
                <FormField control={signUpForm.control} name="email" render={({ field }) => (
                  <FormItem><FormLabel>Correo</FormLabel><FormControl><div className="relative"><Mail className="absolute left-3 top-1/2 h-5 w-5 -translate-y-1/2 text-muted-foreground" /><Input type="email" placeholder="<EMAIL>" {...field} className="pl-10" /></div></FormControl><FormMessage /></FormItem>
                )} />
                <FormField control={signUpForm.control} name="password" render={({ field }) => (
                  <FormItem><FormLabel>Contraseña</FormLabel><FormControl><div className="relative"><KeyRound className="absolute left-3 top-1/2 h-5 w-5 -translate-y-1/2 text-muted-foreground" /><Input type="password" placeholder="••••••••" {...field} className="pl-10" /></div></FormControl><FormMessage /></FormItem>
                )} />
                 <FormField control={signUpForm.control} name="confirmPassword" render={({ field }) => (
                  <FormItem><FormLabel>Confirmar Contraseña</FormLabel><FormControl><div className="relative"><KeyRound className="absolute left-3 top-1/2 h-5 w-5 -translate-y-1/2 text-muted-foreground" /><Input type="password" placeholder="••••••••" {...field} className="pl-10" /></div></FormControl><FormMessage /></FormItem>
                )} />
                <Button type="submit" disabled={loading} className="w-full"><UserPlus className="mr-2 h-5 w-5" /> Crear Cuenta</Button>
              </form></Form>
            </TabsContent>
          </Tabs>
          
          <div className="mt-6 flex items-center"><div className="flex-grow border-t"></div><span className="mx-4 text-xs text-muted-foreground">O</span><div className="flex-grow border-t"></div></div>

          <Button onClick={handleGoogleSignIn} disabled={loading} variant="outline" className="w-full mt-4">
            <svg className="mr-2 h-5 w-5" aria-hidden="true" focusable="false" data-prefix="fab" data-icon="google" role="img" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 488 512"><path fill="currentColor" d="M488 261.8C488 403.3 391.1 504 248 504 110.8 504 0 393.2 0 256S110.8 8 248 8c66.8 0 123 24.5 166.3 64.9l-67.5 64.9C258.5 52.6 94.3 116.6 94.3 256c0 86.5 69.1 156.6 153.7 156.6 98.2 0 135-70.4 140.8-106.9H248v-85.3h236.1c2.3 12.7 3.9 24.9 3.9 41.4z"></path></svg>
            Continuar con Google
          </Button>

          <p className="mt-6 text-xs text-center text-muted-foreground">Al continuar, aceptas nuestros Términos de Servicio y Política de Privacidad.</p>
        </CardContent>
      </Card>
      <p className="mt-8 text-center text-sm text-muted-foreground">Mente en calma &copy; {new Date().getFullYear()}.</p>
    </div>
  );
}
